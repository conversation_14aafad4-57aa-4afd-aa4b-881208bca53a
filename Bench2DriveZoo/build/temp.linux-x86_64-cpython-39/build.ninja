ninja_required_version = 1.3
cxx = c++
nvcc = /public/software/CUDA/cuda-12.1/bin/nvcc

cflags = -pthread -B /public/home/<USER>/miniconda3/envs/transfuser_origin/compiler_compat -Wno-unused-result -Wsign-compare -DNDEBUG -O2 -Wall -fPIC -O2 -isystem /public/home/<USER>/miniconda3/envs/transfuser_origin/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/include -fPIC -O2 -isystem /public/home/<USER>/miniconda3/envs/transfuser_origin/include -fPIC -DWITH_CUDA -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/torch/csrc/api/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/TH -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/THC -I/public/software/CUDA/cuda-12.1/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/torch/csrc/api/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/TH -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/THC -I/public/software/CUDA/cuda-12.1/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/torch/csrc/api/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/TH -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/THC -I/public/software/CUDA/cuda-12.1/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/torch/csrc/api/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/TH -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/THC -I/public/software/CUDA/cuda-12.1/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/torch/csrc/api/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/TH -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/THC -I/public/software/CUDA/cuda-12.1/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/torch/csrc/api/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/TH -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/THC -I/public/software/CUDA/cuda-12.1/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/torch/csrc/api/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/TH -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/THC -I/public/software/CUDA/cuda-12.1/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/torch/csrc/api/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/TH -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/THC -I/public/software/CUDA/cuda-12.1/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/torch/csrc/api/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/TH -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/THC -I/public/software/CUDA/cuda-12.1/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/torch/csrc/api/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/TH -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/THC -I/public/software/CUDA/cuda-12.1/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/torch/csrc/api/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/TH -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/THC -I/public/software/CUDA/cuda-12.1/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/torch/csrc/api/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/TH -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/THC -I/public/software/CUDA/cuda-12.1/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/torch/csrc/api/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/TH -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/THC -I/public/software/CUDA/cuda-12.1/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/torch/csrc/api/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/TH -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/THC -I/public/software/CUDA/cuda-12.1/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/include/python3.9 -c
post_cflags = -DTORCH_API_INCLUDE_EXTENSION_H '-DPYBIND11_COMPILER_TYPE="_gcc"' '-DPYBIND11_STDLIB="_libstdcpp"' '-DPYBIND11_BUILD_ABI="_cxxabi1011"' -DTORCH_EXTENSION_NAME=feature_decorator_ext -D_GLIBCXX_USE_CXX11_ABI=0 -std=c++17
cuda_cflags = -DWITH_CUDA -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/torch/csrc/api/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/TH -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/THC -I/public/software/CUDA/cuda-12.1/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/torch/csrc/api/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/TH -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/THC -I/public/software/CUDA/cuda-12.1/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/torch/csrc/api/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/TH -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/THC -I/public/software/CUDA/cuda-12.1/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/torch/csrc/api/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/TH -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/THC -I/public/software/CUDA/cuda-12.1/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/torch/csrc/api/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/TH -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/THC -I/public/software/CUDA/cuda-12.1/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/torch/csrc/api/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/TH -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/THC -I/public/software/CUDA/cuda-12.1/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/torch/csrc/api/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/TH -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/THC -I/public/software/CUDA/cuda-12.1/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/torch/csrc/api/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/TH -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/THC -I/public/software/CUDA/cuda-12.1/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/torch/csrc/api/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/TH -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/THC -I/public/software/CUDA/cuda-12.1/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/torch/csrc/api/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/TH -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/THC -I/public/software/CUDA/cuda-12.1/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/torch/csrc/api/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/TH -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/THC -I/public/software/CUDA/cuda-12.1/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/torch/csrc/api/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/TH -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/THC -I/public/software/CUDA/cuda-12.1/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/torch/csrc/api/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/TH -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/THC -I/public/software/CUDA/cuda-12.1/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/torch/csrc/api/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/TH -I/public/home/<USER>/miniconda3/envs/transfuser_origin/lib/python3.9/site-packages/torch/include/THC -I/public/software/CUDA/cuda-12.1/include -I/public/home/<USER>/miniconda3/envs/transfuser_origin/include/python3.9 -c
cuda_post_cflags = -D__CUDA_NO_HALF_OPERATORS__ -D__CUDA_NO_HALF_CONVERSIONS__ -D__CUDA_NO_BFLOAT16_CONVERSIONS__ -D__CUDA_NO_HALF2_OPERATORS__ --expt-relaxed-constexpr --compiler-options ''"'"'-fPIC'"'"'' -D__CUDA_NO_HALF_OPERATORS__ -D__CUDA_NO_HALF_CONVERSIONS__ -D__CUDA_NO_HALF2_OPERATORS__ -gencode=arch=compute_52,code=sm_52 -gencode=arch=compute_75,code=sm_75 -gencode=arch=compute_86,code=sm_86 -DTORCH_API_INCLUDE_EXTENSION_H '-DPYBIND11_COMPILER_TYPE="_gcc"' '-DPYBIND11_STDLIB="_libstdcpp"' '-DPYBIND11_BUILD_ABI="_cxxabi1011"' -DTORCH_EXTENSION_NAME=feature_decorator_ext -D_GLIBCXX_USE_CXX11_ABI=0 -std=c++17
cuda_dlink_post_cflags = 
ldflags = 

rule compile
  command = $cxx -MMD -MF $out.d $cflags -c $in -o $out $post_cflags
  depfile = $out.d
  deps = gcc

rule cuda_compile
  depfile = $out.d
  deps = gcc
  command = $nvcc  $cuda_cflags -c $in -o $out $cuda_post_cflags





build /storage/group/4dvlab/wangjiamin/transfuser_origin/Bench2DriveZoo/build/temp.linux-x86_64-cpython-39/mmcv/ops/feature_decorator/src/feature_decorator.o: compile /storage/group/4dvlab/wangjiamin/transfuser_origin/Bench2DriveZoo/mmcv/ops/feature_decorator/src/feature_decorator.cpp
build /storage/group/4dvlab/wangjiamin/transfuser_origin/Bench2DriveZoo/build/temp.linux-x86_64-cpython-39/mmcv/ops/feature_decorator/src/feature_decorator_cuda.o: cuda_compile /storage/group/4dvlab/wangjiamin/transfuser_origin/Bench2DriveZoo/mmcv/ops/feature_decorator/src/feature_decorator_cuda.cu






