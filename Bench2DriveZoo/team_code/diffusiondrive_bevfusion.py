import os
import json
import datetime
import pathlib
import time
import cv2
import carla
from collections import deque
import math
from collections import OrderedDict
from scipy.optimize import fsolve
import torch
import carla
import numpy as np
from PIL import Image
from torchvision import transforms as T
from Bench2DriveZoo.team_code.pid_controller import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,ForwardController,ParkingController
from Bench2DriveZoo.team_code.planner import RoutePlanner
from leaderboard.autoagents import autonomous_agent
from mmcv import Config
from mmcv.models import build_model
from mmcv.utils import (get_dist_info, init_dist, load_checkpoint,
                        wrap_fp16_model)
from mmcv.datasets.pipelines import Compose
from mmcv.parallel.collate import collate as  mm_collate_to_batch_form
from mmcv.core.bbox import get_box_type
from pyquaternion import Quaternion
from mmcv.datasets.pipelines import to_tensor
from typing import List
from einops import rearrange
from mmcv.parallel import DataContainer as DC
from Bench2DriveZoo.team_code.parking_controller import ParkingPIDController
from mmcv.core.points import LiDARPoints


# SAVE_PATH = os.environ.get('SAVE_PATH', None)
IS_BENCH2DRIVE = os.environ.get('IS_BENCH2DRIVE', None)


def get_entry_point():
    return 'TransfuserParkingAgent'

K_70_deg = np.array([
    [1220.08, 0.0, 800.0],
    [0.0, 1220.08, 450.0],
    [0.0, 0.0, 1.0]
])

K_110_deg = np.array([
    [613.88, 0.0, 800.0],
    [0.0, 613.88, 450.0],
    [0.0, 0.0, 1.0]
])

class TransfuserParkingAgent(autonomous_agent.AutonomousAgent):
    def setup(self, path_to_conf_file):
        self.track = autonomous_agent.Track.SENSORS
        self.steer_step = 0
        self.last_moving_status = 0
        self.last_moving_step = -1
        self.last_steer = 0
        self.forward_controller = ForwardController()
        self.parking_controller = ParkingController()
        self.pre_gear = 1
        # self.pidcontroller = PIDController()
        # self.pidcontroller = ParkingPIDController()
        self.window_size = 5
        self.past_trajectory = []
        self.past_yaw = []
        self.config_path = path_to_conf_file.split('+')[0]
        self.ckpt_path = path_to_conf_file.split('+')[1]
        self.target_position = None
        # 添加停车距离阈值（单位：米）
        self.parking_distance_threshold = 2.0  # 当距离目标车位小于2米时停止
        self.is_near_parking_spot = False  # 标记是否已接近车位
        if IS_BENCH2DRIVE:
            self.save_name = path_to_conf_file.split('+')[-1]
        else:
            self.save_name = '_'.join(map(lambda x: '%02d' % x, (now.month, now.day, now.hour, now.minute, now.second)))
        self.step = -1
        self.wall_start = time.time()
        self.initialized = False
        cfg = Config.fromfile(self.config_path)
        if hasattr(cfg, 'plugin'):
            if cfg.plugin:
                import importlib
                if hasattr(cfg, 'plugin_dir'):
                    plugin_dir = cfg.plugin_dir
                    plugin_dir = os.path.join("Bench2DriveZoo", plugin_dir)
                    _module_dir = os.path.dirname(plugin_dir)
                    _module_dir = _module_dir.split('/')
                    _module_path = _module_dir[0]
                    for m in _module_dir[1:]:
                        _module_path = _module_path + '.' + m
                    print(_module_path)
                    plg_lib = importlib.import_module(_module_path)  
  
        self.model = build_model(cfg.model, train_cfg=cfg.get('train_cfg'), test_cfg=cfg.get('test_cfg'))
        checkpoint = load_checkpoint(self.model, self.ckpt_path, map_location='cpu', strict=True)
        self.model.cuda()
        self.model.eval()
        self.inference_only_pipeline = []
        for inference_only_pipeline in cfg.inference_only_pipeline:
            if inference_only_pipeline["type"] not in ['LoadMultiViewImageFromFilesInCeph','LoadMultiViewImageFromFiles']:
                self.inference_only_pipeline.append(inference_only_pipeline)
        self.inference_only_pipeline = Compose(self.inference_only_pipeline)

        self.takeover = False
        self.stop_time = 0
        self.takeover_time = 0
        self.save_path = None
        self._im_transform = T.Compose([T.ToTensor(), T.Normalize(mean=[0.485,0.456,0.406], std=[0.229,0.224,0.225])])
        self.lat_ref, self.lon_ref = 42.0, 2.0

        control = carla.VehicleControl()
        control.steer = 0.0
        control.throttle = 0.0
        control.brake = 0.0	
        self.prev_control = control
        self.prev_control_cache = []
   
        self.lidar2img = {
        'CAM_FRONT':np.array([[ 1.14251841e+03,  8.00000000e+02,  0.00000000e+00, -9.52000000e+02],
                              [ 0.00000000e+00,  4.50000000e+02, -1.14251841e+03, -8.09704417e+02],
                              [ 0.00000000e+00,  1.00000000e+00,  0.00000000e+00, -1.19000000e+00],
                              [ 0.00000000e+00,  0.00000000e+00,  0.00000000e+00,  1.00000000e+00]]),
        'CAM_FRONT_LEFT':np.array([[ 6.03961325e-14,  1.39475744e+03,  0.00000000e+00, -9.20539908e+02],
                                   [-3.68618420e+02,  2.58109396e+02, -1.14251841e+03, -6.47296750e+02],
                                   [-8.19152044e-01,  5.73576436e-01,  0.00000000e+00, -8.29094072e-01],
                                   [ 0.00000000e+00,  0.00000000e+00,  0.00000000e+00,  1.00000000e+00]]),
        'CAM_FRONT_RIGHT':np.array([[ 1.31064327e+03, -4.77035138e+02,  0.00000000e+00,-4.06010608e+02],
                                    [ 3.68618420e+02,  2.58109396e+02, -1.14251841e+03,-6.47296750e+02],
                                    [ 8.19152044e-01,  5.73576436e-01,  0.00000000e+00,-8.29094072e-01],
                                    [ 0.00000000e+00,  0.00000000e+00,  0.00000000e+00, 1.00000000e+00]]),
        'CAM_BACK':np.array([[-5.60166031e+02, -8.00000000e+02,  0.00000000e+00, -1.28800000e+03],
                     [ 5.51091060e-14, -4.50000000e+02, -5.60166031e+02, -8.58939847e+02],
                     [ 1.22464680e-16, -1.00000000e+00,  0.00000000e+00, -1.61000000e+00],
                     [ 0.00000000e+00,  0.00000000e+00,  0.00000000e+00,  1.00000000e+00]]),
        'CAM_BACK_LEFT':np.array([[-1.14251841e+03,  8.00000000e+02,  0.00000000e+00, -6.84385123e+02],
                                  [-4.22861679e+02, -1.53909064e+02, -1.14251841e+03, -4.96004706e+02],
                                  [-9.39692621e-01, -3.42020143e-01,  0.00000000e+00, -4.92889531e-01],
                                  [ 0.00000000e+00,  0.00000000e+00,  0.00000000e+00,  1.00000000e+00]]),
  
        'CAM_BACK_RIGHT': np.array([[ 3.60989788e+02, -1.34723223e+03,  0.00000000e+00, -1.04238127e+02],
                                    [ 4.22861679e+02, -1.53909064e+02, -1.14251841e+03, -4.96004706e+02],
                                    [ 9.39692621e-01, -3.42020143e-01,  0.00000000e+00, -4.92889531e-01],
                                    [ 0.00000000e+00,  0.00000000e+00,  0.00000000e+00,  1.00000000e+00]])
        }
        self.lidar2cam = {
        'CAM_FRONT':np.array([[ 1.  ,  0.  ,  0.  ,  0.  ],
                              [ 0.  ,  0.  , -1.  , -0.24],
                              [ 0.  ,  1.  ,  0.  , -1.19],
                              [ 0.  ,  0.  ,  0.  ,  1.  ]]),
        'CAM_FRONT_LEFT':np.array([[ 0.57357644,  0.81915204,  0.  , -0.22517331],
                                   [ 0.        ,  0.        , -1.  , -0.24      ],
                                   [-0.81915204,  0.57357644,  0.  , -0.82909407],
                                   [ 0.        ,  0.        ,  0.  ,  1.        ]]),
        'CAM_FRONT_RIGHT':np.array([[ 0.57357644, -0.81915204, 0.  ,  0.22517331],
                                   [ 0.        ,  0.        , -1.  , -0.24      ],
                                   [ 0.81915204,  0.57357644,  0.  , -0.82909407],
                                   [ 0.        ,  0.        ,  0.  ,  1.        ]]),
        'CAM_BACK':np.array([[-1. ,  0.,  0.,  0.  ],
                             [ 0. ,  0., -1., -0.24],
                             [ 0. , -1.,  0., -1.61],
                             [ 0. ,  0.,  0.,  1.  ]]),
     
        'CAM_BACK_LEFT':np.array([[-0.34202014,  0.93969262,  0.  , -0.25388956],
                                  [ 0.        ,  0.        , -1.  , -0.24      ],
                                  [-0.93969262, -0.34202014,  0.  , -0.49288953],
                                  [ 0.        ,  0.        ,  0.  ,  1.        ]]),
  
        'CAM_BACK_RIGHT':np.array([[-0.34202014, -0.93969262,  0.  ,  0.25388956],
                                  [ 0.        ,  0.         , -1.  , -0.24      ],
                                  [ 0.93969262, -0.34202014 ,  0.  , -0.49288953],
                                  [ 0.        ,  0.         ,  0.  ,  1.        ]])
        }
        self.lidar2ego = np.array([[ 0. ,  1. ,  0. , -0.39],
                                   [-1. ,  0. ,  0. ,  0.  ],
                                   [ 0. ,  0. ,  1. ,  1.84],
                                   [ 0. ,  0. ,  0. ,  1.  ]])
        self.cam2ego = dict()
        self.cam2lidar = dict()
        for cam in self.lidar2cam.keys():
            self.cam2ego[cam] = np.dot(self.lidar2ego,np.linalg.inv(self.lidar2cam[cam]))
            self.cam2lidar[cam] = np.linalg.inv(self.lidar2cam[cam])
        self.cam_intrinsics = {
            'CAM_FRONT': K_70_deg,
            'CAM_FRONT_LEFT': K_70_deg,
            'CAM_FRONT_RIGHT': K_70_deg,
            'CAM_BACK': K_110_deg,
            'CAM_BACK_LEFT': K_70_deg,
            'CAM_BACK_RIGHT': K_70_deg
        }
        topdown_extrinsics =  np.array([[0.0, -0.0, -1.0, 50.0], [0.0, 1.0, -0.0, 0.0], [1.0, -0.0, 0.0, -0.0], [0.0, 0.0, 0.0, 1.0]])
        unreal2cam = np.array([[0,1,0,0], [0,0,-1,0], [1,0,0,0], [0,0,0,1]])
        self.coor2topdown = unreal2cam @ topdown_extrinsics
        topdown_intrinsics = np.array([[548.993771650447, 0.0, 256.0, 0], [0.0, 548.993771650447, 256.0, 0], [0.0, 0.0, 1.0, 0], [0, 0, 0, 1.0]])
        self.coor2topdown = topdown_intrinsics @ self.coor2topdown

    def _set_target_position(self,target_pos):
        self.target_position = target_pos
        
    # def reset(self,):
    #     self.steer_step = 0
    #     self.last_moving_status = 0
    #     self.last_moving_step = -1
    #     self.last_steer = 0
    #     self.forward_controller.reset()
    #     self.parking_controller.reset()
    #     self.pre_gear = 1
    #     self.past_trajectory = []
    #     self.past_yaw = []
    #     self.takeover_time = 0
    #     self.stop_time = 0
        
        
    def count_time(self,):
        print(f"NOW TIME:{self.takeover_time}")
        self.takeover_time += 1

    def _init(self):
        try:
            locx, locy = self._global_plan_world_coord[0][0].location.x, self._global_plan_world_coord[0][0].location.y
            lon, lat = self._global_plan[0][0]['lon'], self._global_plan[0][0]['lat']
            EARTH_RADIUS_EQUA = 6378137.0
            def equations(vars):
                x, y = vars
                eq1 = lon * math.cos(x * math.pi / 180) - (locx * x * 180) / (math.pi * EARTH_RADIUS_EQUA) - math.cos(x * math.pi / 180) * y
                eq2 = math.log(math.tan((lat + 90) * math.pi / 360)) * EARTH_RADIUS_EQUA * math.cos(x * math.pi / 180) + locy - math.cos(x * math.pi / 180) * EARTH_RADIUS_EQUA * math.log(math.tan((90 + x) * math.pi / 360))
                return [eq1, eq2]
            initial_guess = [0, 0]
            solution = fsolve(equations, initial_guess)
            self.lat_ref, self.lon_ref = solution[0], solution[1]
        except Exception as e:
            print(e, flush=True)
            self.lat_ref, self.lon_ref = 0, 0      
        # self._route_planner = RoutePlanner(4.0, 50.0, lat_ref=self.lat_ref, lon_ref=self.lon_ref)
        # self._route_planner.set_route(self._global_plan, True)
        self.initialized = True
        self.metric_info = {}
  
    def _set_vehicles(self,vehicle):
        # self.pidcontroller._set_vehicle(vehicle)
        pass
    
    def set_player(self,player):
        self.player = player
  

    def sensors(self):
        sensors =[
                # camera rgb
                {
                    'type': 'sensor.camera.rgb',
                    'x': 0.80, 'y': 0.0, 'z': 1.60,
                    'roll': 0.0, 'pitch': 0.0, 'yaw': 0.0,
                    'width': 1600, 'height': 900, 'fov': 70,
                    'id': 'CAM_FRONT'
                },
                {
                    'type': 'sensor.camera.rgb',
                    'x': 0.27, 'y': -0.55, 'z': 1.60,
                    'roll': 0.0, 'pitch': 0.0, 'yaw': -55.0,
                    'width': 1600, 'height': 900, 'fov': 70,
                    'id': 'CAM_FRONT_LEFT'
                },
                {
                    'type': 'sensor.camera.rgb',
                    'x': 0.27, 'y': 0.55, 'z': 1.60,
                    'roll': 0.0, 'pitch': 0.0, 'yaw': 55.0,
                    'width': 1600, 'height': 900, 'fov': 70,
                    'id': 'CAM_FRONT_RIGHT'
                },
                {
                    'type': 'sensor.camera.rgb',
                    'x': -2.0, 'y': 0.0, 'z': 1.60,
                    'roll': 0.0, 'pitch': 0.0, 'yaw': 180.0,
                    'width': 1600, 'height': 900, 'fov': 110,
                    'id': 'CAM_BACK'
                },
                {
                    'type': 'sensor.camera.rgb',
                    'x': -0.32, 'y': -0.55, 'z': 1.60,
                    'roll': 0.0, 'pitch': 0.0, 'yaw': -110.0,
                    'width': 1600, 'height': 900, 'fov': 70,
                    'id': 'CAM_BACK_LEFT'
                },
                {
                    'type': 'sensor.camera.rgb',
                    'x': -0.32, 'y': 0.55, 'z': 1.60,
                    'roll': 0.0, 'pitch': 0.0, 'yaw': 110.0,
                    'width': 1600, 'height': 900, 'fov': 70,
                    'id': 'CAM_BACK_RIGHT'
                },
                # imu
                {
                    'type': 'sensor.other.imu',
                    'x': -1.4, 'y': 0.0, 'z': 0.0,
                    'roll': 0.0, 'pitch': 0.0, 'yaw': 0.0,
                    'sensor_tick': 0.05,
                    'id': 'IMU'
                },
                # gps
                {
                    'type': 'sensor.other.gnss',
                    'x': -1.4, 'y': 0.0, 'z': 0.0,
                    'roll': 0.0, 'pitch': 0.0, 'yaw': 0.0,
                    'sensor_tick': 0.01,
                    'id': 'GPS'
                },
                # speed
                {
                    'type': 'sensor.speedometer',
                    'reading_frequency': 10,
                    'id': 'SPEED'
                },
                # lidar
                {   'type': 'sensor.lidar.ray_cast',
                    'x': -0.39, 'y': 0.0, 'z': 1.84,
                    'roll': 0.0, 'pitch': 0.0, 'yaw': 0.0,
                    'range': 85,
                    'rotation_frequency': 10,
                    'channels': 64,
                    'points_per_second': 600000,
                    'dropoff_general_rate': 0.0,
                    'dropoff_intensity_limit': 0.0,
                    'dropoff_zero_intensity': 0.0,
                    'id': 'LIDAR_TOP'
                    },
            ]
        if IS_BENCH2DRIVE:
            sensors += [
                    {	
                        'type': 'sensor.camera.rgb',
                        'x': 0.0, 'y': 0.0, 'z': 50.0,
                        'roll': 0.0, 'pitch': -90.0, 'yaw': 0.0,
                        'width': 512, 'height': 512, 'fov': 5 * 10.0,
                        'id': 'bev'
                    }]
        return sensors

    def tick(self, input_data):
        self.step += 1
        print(input_data.keys())
        encode_param = [int(cv2.IMWRITE_JPEG_QUALITY), 20]
        imgs = {}
        cam_intrinsics = self.cam_intrinsics
        cam2ego = self.cam2ego
        lidar2cam = self.lidar2cam
        lidar2img = self.lidar2img
        cam2lidar = self.cam2lidar
        lidar2ego = self.lidar2ego
        for cam in ['CAM_FRONT','CAM_FRONT_LEFT','CAM_FRONT_RIGHT','CAM_BACK','CAM_BACK_LEFT','CAM_BACK_RIGHT']:
            img = cv2.cvtColor(input_data[cam][1][:, :, :3], cv2.COLOR_BGR2RGB)
            _, img = cv2.imencode('.jpg', img, encode_param)
            img = cv2.imdecode(img, cv2.IMREAD_COLOR)
            imgs[cam] = img
        bev = cv2.cvtColor(input_data['bev'][1][:, :, :3], cv2.COLOR_BGR2RGB)
        # gps = input_data['GPS'][1][:2]
        location = self.player.get_transform().location
        rotation = self.player.get_transform().rotation
        print(f"GOT LOCATION:{location}")
        gps = np.array([location.x,location.y])
        speed = input_data['SPEED'][1]['speed']
        compass = input_data['IMU'][1][-1]
        # compass = math.radians(rotation.yaw)
        acceleration = input_data['IMU'][1][:3]
        angular_velocity = input_data['IMU'][1][3:6]
        points = input_data['LIDAR_TOP'][1][:,:3]
        num_points = np.array([points.shape[0]]).astype(np.int32)
        # print(f"GOT POINTS SHAPE:{points.shape}")
        pos = gps
        target_position = self.target_position
        
        print(f"GOT pos:{pos} GOT target:{self.target_position}")
        # near_node, near_command = self._route_planner.run_step(pos)
  
        if (math.isnan(compass) == True): #It can happen that the compass sends nan for a few frames
            compass = 0.0
            acceleration = np.zeros(3)
            angular_velocity = np.zeros(3)

        result = {
                'imgs': imgs,
                'gps': gps,
                'pos':pos,
                'speed': speed,
                'compass': compass,
                'bev': bev,
                'acceleration':acceleration,
                'angular_velocity':angular_velocity,
                # 'command_near':near_command,
                # 'command_near_xy':near_node,
                'num_points':num_points,
                'points':points,
                'cam2ego_rts': cam2ego,
                'lidar2img': lidar2img,
                'lidar2cam': lidar2cam,
                'lidar2ego': lidar2ego,
                'cam2lidar_rts': cam2lidar,
                'cam_intrinsic': cam_intrinsics,
                'target_position': target_position,
                }
        
        return result
    
    @torch.no_grad()
    def run_step(self, input_data, timestamp):
        # print(f"input_data key:{input_data.keys()}")
        if not self.initialized:
            self._init()
        tick_data = self.tick(input_data)
        # print(f"GOT TICK DATA KEYS:{tick_data.keys()}")
        results = {}
        results['lidar2img'] = []
        results['lidar2cam'] = []
        results['img'] = []
        results['folder'] = ' '
        results['scene_token'] = ' '  
        results['frame_idx'] = 0
        results['timestamp'] = self.step / 20
        results['box_type_3d'], _ = get_box_type('LiDAR')
        results['cam2ego'] = []
        results['intrinsic'] = []
        results['cam2lidar_rts'] = []
        results['cam2ego_rts'] = []
        results['cam_intrinsic'] = []
        results['past_trajectory'] = []
        # print(f"GOT RESULT KEYS:{results.keys()}")
        for cam in ['CAM_FRONT','CAM_FRONT_LEFT','CAM_FRONT_RIGHT','CAM_BACK','CAM_BACK_LEFT','CAM_BACK_RIGHT']:
            results['lidar2img'].append(self.lidar2img[cam])
            results['lidar2cam'].append(self.lidar2cam[cam])
            results['img'].append(tick_data['imgs'][cam])
            results['cam2ego_rts'].append(tick_data['cam2ego_rts'][cam])
            results['cam_intrinsic'].append(tick_data['cam_intrinsic'][cam])
            results['cam2lidar_rts'].append(tick_data['cam2lidar_rts'][cam])
        results['lidar2ego'] = tick_data['lidar2ego']
        results['lidar2img'] = np.stack(results['lidar2img'],axis=0)
        results['lidar2cam'] = np.stack(results['lidar2cam'],axis=0)
        results['img'] = np.stack(results['img'],axis=0)
        results['cam2ego_rts'] = np.stack(results['cam2ego_rts'],axis=0)
        results['cam_intrinsic'] = np.stack(results['cam_intrinsic'],axis=0)
        results['cam2lidar_rts'] = np.stack(results['cam2lidar_rts'],axis=0)
        results['points'] = tick_data['points']
        results['num_points'] = tick_data['num_points']
        
        raw_theta = tick_data['compass']   if not np.isnan(tick_data['compass']) else 0
        ego_theta = -raw_theta + np.pi/2
        rotation = list(Quaternion(axis=[0, 0, 1], radians=ego_theta))
        can_bus = np.zeros(19)
        can_bus[0] = tick_data['pos'][0]
        can_bus[1] = -tick_data['pos'][1]
        can_bus[3:7] = rotation
        can_bus[7] = tick_data['speed']
        speed_x = (can_bus[7] * np.cos(ego_theta))
        speed_y = (can_bus[7] * np.sin(ego_theta))
        can_bus[7] = speed_x
        can_bus[8] = speed_y
        print(f"GOT SPEED_X:{speed_x} SPEED_Y:{speed_y}")
        can_bus[10:13] = tick_data['acceleration']
        can_bus[11] *= -1
        can_bus[13:16] = -tick_data['angular_velocity']
        can_bus[16] = ego_theta
        can_bus[17] = ego_theta / np.pi * 180 
        can_bus[18] = self.pre_gear
        results['can_bus'] = can_bus
        ego_lcf_feat = np.zeros(9)
        ego_lcf_feat[0:2] = can_bus[0:2].copy()
        ego_lcf_feat[2:4] = can_bus[10:12].copy()
        ego_lcf_feat[4] = rotation[-1]
        ego_lcf_feat[5] = 4.89238167
        ego_lcf_feat[6] = 1.83671331
        ego_lcf_feat[7] = np.sqrt(can_bus[0]**2+can_bus[1]**2)
        if len(self.prev_control_cache)<10:
            ego_lcf_feat[8] = 0
        else:
            ego_lcf_feat[8] = self.prev_control_cache[0].steer

        command = 3
        results['command'] = command
        command_onehot = np.zeros(4)
        command_onehot[command] = 1
        results['ego_fut_cmd'] = command_onehot
        theta_to_lidar = raw_theta
        rotation_matrix = np.array([[np.cos(theta_to_lidar),-np.sin(theta_to_lidar)],[np.sin(theta_to_lidar),np.cos(theta_to_lidar)]])
  
        ego2world = np.eye(4)
        ego2world[0:3,0:3] = Quaternion(axis=[0, 0, 1], radians=ego_theta).rotation_matrix
        ego2world[0:2,3] = can_bus[0:2]
        lidar2global = ego2world @ self.lidar2ego
        
        target_position = tick_data['target_position']
        target_position = np.append(target_position,1)
        target_position = np.append(target_position,1)
        print(f"SELF TARGET POSITION:{target_position}")
        world2ego = np.linalg.inv(ego2world)
        left2right = np.eye(4)
        left2right[1,1] = -1
        target_position = world2ego @ target_position
        target_position = target_position[:2]
        results['target_position'] = target_position
        print(f"input target:{results['target_position']}")
        past_trajectory = np.array(self.past_trajectory)
        past_yaw = np.array(self.past_yaw)
        if past_trajectory.shape[0] != 0:
            past_trajectory = (world2ego @ past_trajectory.T).T
            # past_yaw_copy = -(past_yaw - np.pi / 2)
            past_yaw = (past_yaw - tick_data['compass']) + np.pi / 2
            for i in range(len(past_yaw)):
                while past_yaw[i] > np.pi:
                    past_yaw[i] -= np.pi * 2
                while past_yaw[i] < -np.pi:
                    past_yaw[i] += np.pi * 2
            
            if len(self.past_trajectory) < self.window_size:
                now_len = len(self.past_trajectory)
                padding_past_trajectory = np.array([past_trajectory[0] for i in range(self.window_size - now_len)])
                padding_past_yaw = np.array([past_yaw[0] for i in range(self.window_size - now_len)])
                past_trajectory = np.concatenate([padding_past_trajectory,past_trajectory],axis=0)
                past_yaw = np.concatenate([padding_past_yaw,past_yaw],axis=0)
            past_yaw = past_yaw[:,None]
        else:
            past_trajectory = np.array([[0,0,0,0] for i in range(self.window_size)])
            past_yaw = np.array([[0] for i in range(self.window_size)])
        past_trajectory = past_trajectory[:,:2]
        # past_yaw = np.arctan2(past_trajectory[:,0],past_trajectory[:,1])
        # past_yaw = -past_yaw + np.pi / 2
        # for i in range(len(past_yaw)):
        #     while past_yaw[i] > np.pi:
        #         past_yaw[i] -= np.pi*2
        #     while past_yaw[i] < -np.pi:
        #         past_yaw[i] += np.pi*2
        # past_yaw = past_yaw[:,None]
        past_trajectory = np.concatenate([past_trajectory[:,0:1],past_trajectory[:,1:2],past_yaw[:,]],axis=-1)
        # print("GOT PAST TRAJECTORY")
        # print(self.past_trajectory)
        print("GOT RELATIVE PAST TRAJECTORY")
        print(past_trajectory)
        results['past_trajectory'] = past_trajectory
        # results['l2g_r_mat'] = lidar2global[0:3,0:3]
        # results['l2g_t'] = lidar2global[0:3,3]
        # for key,value in results.items():
        #     print(f"key:{key} value:{type(value)}")
        if self.takeover_time % 5 == 0:
            self.past_trajectory.append([tick_data['pos'][0],-tick_data['pos'][1],1,1])
            self.past_yaw.append(tick_data['compass'])
            if len(self.past_trajectory) == self.window_size + 1:
                self.past_trajectory.pop(0)
                self.past_yaw.pop(0)
        stacked_imgs = np.stack(results['img'],axis=-1)
        results['img_shape'] = stacked_imgs.shape
        results['ori_shape'] = stacked_imgs.shape
        results['pad_shape'] = stacked_imgs.shape
        results = self.inference_only_pipeline(results)
        self.device="cuda"
        results['target_position'] = DC(torch.stack([to_tensor(results['target_position'])]),cpu_only=False,stack=True,pad_dims=1)
        results['img'] = to_tensor(results['img'])
        results['img'] = rearrange(results['img'],'n h w c -> n c h w')
        results['img'] = DC(torch.stack([results['img']]),cpu_only=False,stack=True)
        results['points'] = DC(to_tensor(results['points']),cpu_only=False)
        results['cam2ego_rts'] = DC(torch.stack([to_tensor(results['cam2ego_rts'])]),cpu_only=False,stack=True)
        results['lidar2img'] = DC(torch.stack([to_tensor(results['lidar2img'])]),cpu_only=False,stack=True)
        results['lidar2cam'] = DC(torch.stack([to_tensor(results['lidar2cam'])]),cpu_only=False,stack=True)
        results['lidar2ego'] = DC(torch.stack([to_tensor(results['lidar2ego'])]),cpu_only=False,stack=True)
        results['cam2lidar_rts'] = DC(torch.stack([to_tensor(results['cam2lidar_rts'])]),cpu_only=False,stack=True)
        results['cam_intrinsic'] = DC(torch.stack([to_tensor(results['cam_intrinsic'])]),cpu_only=False,stack=True)
        results['can_bus'] = to_tensor(results['can_bus'])
        results['ego_fut_cmd'] = to_tensor(results['ego_fut_cmd'])
        results['past_trajectory'] = DC(torch.stack([to_tensor(results['past_trajectory']).to(torch.float32)]),cpu_only=False,stack=True)
        input_data_batch = mm_collate_to_batch_form([results], samples_per_gpu=1)
        input_data_batch['infer_type'] = torch.full((1,1),2).to(torch.long)
        for key,data in input_data_batch.items():
            if key == 'img_metas':
                continue
            # print(f"key:{key} List:{isinstance(data,List)} Tensor:{isinstance(data,torch.Tensor)} device:{self.device}")
            if isinstance(data,List):
                data[0] = data[0].to(self.device)
            elif isinstance(data,torch.Tensor):
                # print("GOT KEY")
                input_data_batch[key] = data.to(self.device)
        # for key, data in input_data_batch.items():
        #     if key != 'img_metas':
        #         print(type(data[0]))
        #         if torch.is_tensor(data[0]):
        #             data[0] = data[0].to(self.device)
        #         elif isinstance(data,torch.Tensor):
        #             data = data.to(self.device)
        # for key,value in results.items():
        #     print(f"key:{key} value:{type(value)}")

        # for key,data in input_data_batch.items():
        #     print(f"key:{key} data:{type(data)} data[0] type:{type(data[0])} shape:{data[0].shape}")
        output_data_batch = self.model(input_data_batch, return_loss=False)
        # throttle = output_data_batch['throttle'][0]
        # brake = output_data_batch['brake'][0]
        # steer = output_data_batch['steer'][0]
        # gear = torch.sigmoid(output_data_batch['gear'])[0]
        out_truck = output_data_batch['trajectory'][0].cpu().numpy()
        out_truck[:,-1] -= math.pi/2
        use_brake = False
        print(f"TRAJECTORY:{out_truck}")

        # 计算当前位置到目标车位的距离
        if self.target_position is not None:
            current_pos = tick_data['pos']  # [x, y]
            target_pos = self.target_position  # [x, y]
            distance_to_target = math.sqrt((current_pos[0] - target_pos[0])**2 + (current_pos[1] - target_pos[1])**2)
            print(f"Distance to parking spot: {distance_to_target:.2f} meters")

            # 如果距离小于阈值，停车
            if distance_to_target < self.parking_distance_threshold:
                self.is_near_parking_spot = True
                print(f"Near parking spot! Stopping vehicle.")
                control = carla.VehicleControl()
                control.steer = 0.0
                control.throttle = 0.0
                control.brake = 1.0
                control.hand_brake = True
                control.reverse = False
                self.prev_control = control
                if len(self.prev_control_cache)==10:
                    self.prev_control_cache.pop(0)
                self.prev_control_cache.append(control)
                return control

        if out_truck[-1][0] < 0: # R geer
            # out_truck = out_truck[out_truck[:,1]<=0]
            if self.pre_gear == 1:
                self.forward_controller.reset()
                use_brake = True
            gear,manual_gear_shift,steer_traj,throttle_traj,brake_traj,metadata_traj = self.parking_controller.control_pid(out_truck, tick_data['speed'])
            if brake_traj < 0.05: brake_traj = 0.0
            if throttle_traj > brake_traj: brake_traj = 0.0
            control = carla.VehicleControl()
            self.pid_metadata = metadata_traj
            self.pid_metadata['agent'] = 'only_traj'
            control.steer = np.clip(float(steer_traj), -1, 1)
            control.throttle = np.clip(float(throttle_traj), 0.0, 0.75)
            control.brake = np.clip(float(brake_traj), 0, 1)     
            if use_brake:
                control.brake = 1.0
                use_brake = False
            # control.brake = 0
            control.reverse = True
            control.gear = gear
            self.pre_gear = gear
            # print(f"pid steer:{control.steer} pred steer:{steer}")
            # print(f"pid throttle:{control.throttle} pred throttle:{throttle}")
            # print(f"pid brake:{control.brake} pred brake:{brake}")
            # print(f"pid gear:{control.gear} pred gear:{gear}")
            # print(f"speed:{tick_data['speed']}")
            print(f"pid steer:{control.steer} ")
            print(f"pid throttle:{control.throttle}")
            print(f"pid brake:{control.brake}")
            print(f"pid gear:{control.gear}")
            print(f"speed:{tick_data['speed']}")
            self.pid_metadata['steer'] = control.steer
            self.pid_metadata['throttle'] = control.throttle
            self.pid_metadata['brake'] = control.brake
            self.pid_metadata['steer_traj'] = float(steer_traj)
            self.pid_metadata['throttle_traj'] = float(throttle_traj)
            self.pid_metadata['brake_traj'] = float(brake_traj)
            self.pid_metadata['plan'] = out_truck.tolist()
            self.pid_metadata['command'] = command
        else:
            if self.pre_gear == -1:
                self.parking_controller.reset()
                use_brake = False
            gear,manual_gear_shift,steer_traj, throttle_traj, brake_traj, metadata_traj = self.forward_controller.control_pid(out_truck, tick_data['speed'])
            if brake_traj < 0.05: brake_traj = 0.0
            if throttle_traj > brake_traj: brake_traj = 0.0
            control = carla.VehicleControl()
            self.pid_metadata = metadata_traj
            self.pid_metadata = {}
            self.pid_metadata['agent'] = 'only_traj'
            control.steer = np.clip(float(steer_traj), -1, 1)
            control.throttle = np.clip(float(throttle_traj), 0.0, 0.75)
            control.brake = np.clip(float(brake_traj), 0, 1)     
            if use_brake:
                control.brake = 1.0
            # control.brake = 0
            control.reverse = False
            control.gear = gear
            self.pre_gear = gear
            # print(f"pid steer:{control.steer} pred steer:{steer}")
            # print(f"pid throttle:{control.throttle} pred throttle:{throttle}")
            # print(f"pid brake:{control.brake} pred brake:{brake}")
            # print(f"pid gear:{control.gear} pred gear:{gear}")
            # print(f"speed:{tick_data['speed']}")
            print(f"pid steer:{control.steer} ")
            print(f"pid throttle:{control.throttle}")
            print(f"pid brake:{control.brake}")
            print(f"pid gear:{control.gear}")
            print(f"speed:{tick_data['speed']}")
            self.pid_metadata['steer'] = control.steer
            self.pid_metadata['throttle'] = control.throttle
            self.pid_metadata['brake'] = control.brake
            self.pid_metadata['steer_traj'] = float(steer_traj)
            self.pid_metadata['throttle_traj'] = float(throttle_traj)
            self.pid_metadata['brake_traj'] = float(brake_traj)
            self.pid_metadata['plan'] = out_truck.tolist()
            self.pid_metadata['command'] = command

        
        # self.pid_metadata['all_plan'] = all_out_truck.tolist()

        metric_info = self.get_metric_info()
        self.metric_info[self.step] = metric_info
        if self.save_path is not None and self.step % 1 == 0:
            self.save(tick_data)
        self.prev_control = control
        
        if len(self.prev_control_cache)==10:
            self.prev_control_cache.pop(0)
        self.prev_control_cache.append(control)
        # control = carla.VehicleControl()
        return control

    def set_save_path(self,save_path):
        self.save_path = save_path
        if save_path is not None:
            self.save_path.mkdir(parents=True, exist_ok=False)
            (self.save_path / 'rgb_front').mkdir()
            (self.save_path / 'rgb_front_right').mkdir()
            (self.save_path / 'rgb_front_left').mkdir()
            (self.save_path / 'rgb_back').mkdir()
            (self.save_path / 'rgb_back_right').mkdir()
            (self.save_path / 'rgb_back_left').mkdir()
            (self.save_path / 'meta').mkdir()
            (self.save_path / 'bev').mkdir()

    def save(self, tick_data):
        frame = self.step // 10

        Image.fromarray(tick_data['imgs']['CAM_FRONT']).save(self.save_path / 'rgb_front' / ('%04d.png' % frame))
        Image.fromarray(tick_data['imgs']['CAM_FRONT_LEFT']).save(self.save_path / 'rgb_front_left' / ('%04d.png' % frame))
        Image.fromarray(tick_data['imgs']['CAM_FRONT_RIGHT']).save(self.save_path / 'rgb_front_right' / ('%04d.png' % frame))
        Image.fromarray(tick_data['imgs']['CAM_BACK']).save(self.save_path / 'rgb_back' / ('%04d.png' % frame))
        Image.fromarray(tick_data['imgs']['CAM_BACK_LEFT']).save(self.save_path / 'rgb_back_left' / ('%04d.png' % frame))
        Image.fromarray(tick_data['imgs']['CAM_BACK_RIGHT']).save(self.save_path / 'rgb_back_right' / ('%04d.png' % frame))
        Image.fromarray(tick_data['bev']).save(self.save_path / 'bev' / ('%04d.png' % frame))

        Image.fromarray(tick_data['bev']).save(self.save_path / 'bev' / '-1.png')

        outfile = open(self.save_path / 'meta' / ('%04d.json' % frame), 'w')
        json.dump(self.pid_metadata, outfile, indent=4)
        outfile.close()

        # metric info
        outfile = open(self.save_path / 'metric_info.json', 'w')
        json.dump(self.metric_info, outfile, indent=4)
        outfile.close()

    def destroy(self):
        del self.model
        torch.cuda.empty_cache()

    def gps_to_location(self, gps):
        EARTH_RADIUS_EQUA = 6378137.0
        # gps content: numpy array: [lat, lon, alt]
        lat, lon = gps
        scale = math.cos(self.lat_ref * math.pi / 180.0)
        my = math.log(math.tan((lat+90) * math.pi / 360.0)) * (EARTH_RADIUS_EQUA * scale)
        mx = (lon * (math.pi * EARTH_RADIUS_EQUA * scale)) / 180.0
        y = scale * EARTH_RADIUS_EQUA * math.log(math.tan((90.0 + self.lat_ref) * math.pi / 360.0)) - my
        x = mx - scale * self.lon_ref * math.pi * EARTH_RADIUS_EQUA / 180.0
        return np.array([x, y])
