from collections import deque
import numpy as np
import math

class PID(object):
    def __init__(self, K_P=1.0, K_I=0.0, K_D=0.0, n=20):
        self._K_P = K_P
        self._K_I = K_I
        self._K_D = K_D
        self._window = deque([0 for _ in range(n)], maxlen=n)
        self._max = 0.0
        self._min = 0.0
        self._n = n
        
    def reset(self):
        self._window = deque([0 for _ in range(self._n)], maxlen=self._n)
        self._max = 0.0
        self._min = 0.0

    def step(self, error):
        self._window.append(error)
        self._max = max(self._max, abs(error))
        self._min = -abs(self._max)

        if len(self._window) >= 2:
            integral = np.mean(self._window)
            derivative = (self._window[-1] - self._window[-2])
        else:
            integral = 0.0
            derivative = 0.0

        return self._K_P * error + self._K_I * integral + self._K_D * derivative

class HeadingPID(object):
    def __init__(self, K_P=1.0, K_I=0.0, K_D=0.0, n=20):
        self._K_P = K_P
        self._K_I = K_I
        self._K_D = K_D
        self._window = deque([0 for _ in range(n)], maxlen=n)
        self._max = 0.0
        self._min = 0.0
        self._n = n
        
    def reset(self):
        self._window = deque([0 for _ in range(self._n)], maxlen=self._n)
        self._max = 0.0
        self._min = 0.0

    def step(self, error):
        self._window.append(error)
        self._max = max(self._max, abs(error))
        self._min = -abs(self._max)

        if len(self._window) >= 2:
            integral = np.mean(self._window)
            derivative = (self._window[-1] - self._window[-2])
        else:
            integral = 0.0
            derivative = 0.0

        return self._K_P * error + self._K_I * integral + self._K_D * derivative

class PIDController(object):
    
    def __init__(self, turn_KP=0.75, turn_KI=0.75, turn_KD=0.3, turn_n=40, speed_KP=5.0, speed_KI=0.5,speed_KD=1.0, speed_n = 40,max_throttle=0.75, brake_speed=0.4,brake_ratio=1.1, clip_delta=0.25, aim_dist=4.0, angle_thresh=0.3, dist_thresh=10):
        
        self.turn_controller = PID(K_P=turn_KP, K_I=turn_KI, K_D=turn_KD, n=turn_n)
        self.speed_controller = PID(K_P=speed_KP, K_I=speed_KI, K_D=speed_KD, n=speed_n)
        self.max_throttle = max_throttle
        self.brake_speed = brake_speed
        self.brake_ratio = brake_ratio
        self.clip_delta = clip_delta
        self.aim_dist = aim_dist
        self.angle_thresh = angle_thresh
        self.dist_thresh = dist_thresh
        self.desired_speed = 0

    def reset(self):
        self.turn_controller.reset()
        self.speed_controller.reset()

    def control_pid(self, waypoints, speed, target=None):
        ''' Predicts vehicle control with a PID controller.
        Args:
            waypoints (tensor): output of self.plan()
            speed (tensor): speedometer input
        '''

        # iterate over vectors between predicted waypoints
        num_pairs = len(waypoints) - 1
        best_norm = 1e5
        # desired_speed = np.array([self.desired_speed])
        distance = math.sqrt(waypoints[-1][0]**2 + waypoints[-1][1]**2) / 4. * 3.6
        desired_speed = np.array([distance])
        # desired_speed = 0
        aim = waypoints[0]
        for i in range(num_pairs):
            # # magnitude of vectors, used for speed
            # desired_speed += np.linalg.norm(
            #         waypoints[i+1] - waypoints[i]) * 2.0 / num_pairs

            # norm of vector midpoints, used for steering
            norm = np.linalg.norm((waypoints[i+1] + waypoints[i]) / 2.0)
            if abs(self.aim_dist-best_norm) > abs(self.aim_dist-norm):
                aim = waypoints[i]
                best_norm = norm

        aim_last = waypoints[-1] - waypoints[-2]
        
        # if aim[1] < 0:
        #     angle = np.degrees(np.pi / 2 - np.arctan2(abs(aim[1]), abs(aim[0]))) / 90
        # else:
        angle = np.degrees(np.pi / 2 - np.arctan2(aim[1], aim[0])) / 90
            
        angle_last = np.degrees(np.pi / 2 - np.arctan2(aim_last[1], aim_last[0])) / 90
        # angle_target = np.degrees(np.pi / 2 - np.arctan2(target[1], target[0])) / 90
        use_heading_control = True
        if use_heading_control:
            angle_last = waypoints[-1][2]
            angle_last = np.degrees(-angle_last) / 90

        # choice of point to aim for steering, removing outlier predictions
        # use target point if it has a smaller angle or if error is large
        # predicted point otherwise
        # (reduces noise in eg. straight roads, helps with sudden turn commands)
        use_target_to_aim = False
        # use_target_to_aim = np.abs(angle_target) < np.abs(angle)
        # use_target_to_aim = use_target_to_aim or (np.abs(angle_target-angle_last) > self.angle_thresh and target[1] < self.dist_thresh)
        if use_target_to_aim:
            angle_final = angle_target
        else:
            angle_final = angle_last
        

        steer = self.turn_controller.step(angle_final)
        steer = np.clip(steer, -1.0, 1.0)

        brake = desired_speed < self.brake_speed or (speed / desired_speed) > self.brake_ratio

        delta = np.clip(desired_speed - speed, 0, self.clip_delta)
        throttle = self.speed_controller.step(delta)
        throttle = np.clip(throttle, 0.0, self.max_throttle)
        throttle = throttle if not brake else 0.0

        metadata = {
            'speed': float(speed.astype(np.float64)),
            'steer': float(steer),
            'throttle': float(throttle),
            'brake': float(brake),
            'wp_4': tuple(waypoints[3].astype(np.float64)),
            'wp_3': tuple(waypoints[2].astype(np.float64)),
            'wp_2': tuple(waypoints[1].astype(np.float64)),
            'wp_1': tuple(waypoints[0].astype(np.float64)),
            'aim': tuple(aim.astype(np.float64)),
            # 'target': tuple(target.astype(np.float64)),
            'desired_speed': float(desired_speed.astype(np.float64)),
            'angle': float(angle.astype(np.float64)),
            'angle_last': float(angle_last.astype(np.float64)),
            # 'angle_target': float(angle_target.astype(np.float64)),
            'angle_final': float(angle_final.astype(np.float64)),
            'delta': float(delta.astype(np.float64)),
            'waypoints':waypoints,
        }

        return steer, throttle, brake, metadata



class ForwardController(PIDController):
    def __init__(self, turn_KP=1.0, turn_KI=0.5, turn_KD=0.8, turn_n=40, speed_KP=5.0, speed_KI=0.5,speed_KD=1.0, speed_n = 40,max_throttle=0.75, brake_speed=0.1,brake_ratio=1.1, clip_delta=0.25, aim_dist=4.0, angle_thresh=0.3, dist_thresh=10):
        super().__init__(turn_KP, turn_KI, turn_KD, turn_n, speed_KP, speed_KI,speed_KD, speed_n,max_throttle, brake_speed,brake_ratio, clip_delta, aim_dist, angle_thresh, dist_thresh)

    def control_pid(self,waypoints,speed,targets=None):
        steer, throttle, brake, metadata = super().control_pid(waypoints,speed,targets)
        gear = 1
        manual_gear_shift = True
        return gear,manual_gear_shift,steer,throttle,brake,metadata
    
    def _set_desired_speed(self,speed):
        self.desired_speed = speed

#region oldparkingpid
# class ParkingController(PIDController):
#     def __init__(self, turn_KP=5.0, turn_KI=1.0, turn_KD=1.0, turn_n=20, speed_KP=5.0, speed_KI=0.5, speed_KD=1.0, speed_n=40, max_throttle=0.75, brake_speed=0.4, brake_ratio=1.1, clip_delta=0.25, aim_dist=1.0, angle_thresh=0.3, dist_thresh=10):
#         # super().__init__(turn_KP, turn_KI, turn_KD, turn_n, speed_KP, speed_KI, speed_KD, speed_n, max_throttle, brake_speed, brake_ratio, clip_delta, aim_dist, angle_thresh, dist_thresh)
#         self.turn_controller = PID(K_P=turn_KP, K_I=turn_KI, K_D=turn_KD, n=turn_n)
#         self.speed_controller = PID(K_P=speed_KP, K_I=speed_KI, K_D=speed_KD, n=speed_n)
#         self.max_throttle = max_throttle
#         self.brake_speed = brake_speed
#         self.brake_ratio = brake_ratio
#         self.clip_delta = clip_delta
#         self.aim_dist = aim_dist
#         self.angle_thresh = angle_thresh
#         self.dist_thresh = dist_thresh
#         self.desired_speed = 0
#         self.turn_KP = turn_KP
#         self.turn_KI = turn_KI
#         self.turn_KD = turn_KD
#         self.turn_n = turn_n

#     def control_pid(self, waypoints, speed, targets=None):
#         print(f"WAYPOINTS SHAPE:{waypoints.shape}")
#         # waypoints[:,1] = - waypoints[:,1]
#         num_pairs = len(waypoints) - 1
#         best_norm = 1e5
#         desired_speed = np.array([-0.8])
#         aim = waypoints[0]
#         for i in range(num_pairs):
#             norm = np.linalg.norm((waypoints[i+1]+waypoints[i])/2.0)
#             if abs(self.aim_dist - best_norm) > abs(self.aim_dist-norm):
#                 aim = waypoints[i]
#                 best_norm = norm
#         aim_last = waypoints[-1] - waypoints[-2]
          
#         if aim[1] < 0:
#             angle = np.degrees(np.pi / 2 - np.arctan2(-aim[1], -aim[0])) / 90
#         else:
#             angle = np.degrees(np.pi / 2 - np.arctan2(aim[1], aim[0])) / 90
            
#         if aim_last[1]<0:
#             angle_last = np.degrees(np.pi / 2 - np.arctan2(-aim_last[1], -aim_last[0])) / 90
#         else:
#             angle_last = np.degrees(np.pi / 2 - np.arctan2(aim_last[1], aim_last[0])) / 90
#         use_heading_control = True
#         if use_heading_control:
#             # angle_last = np.degrees(-angle_last) / 90
#             angle_last = waypoints[-1][2]
#             # if angle_last > np.pi / 2:
#             #     angle_last -= np.pi
#             # elif angle_last < -np.pi / 2:
#             #     angle_last += np.pi
#             angle_last = np.degrees(angle_last) / 90
#         # angle_last = np.degrees(np.pi / 2 - np.clip(np.arctan2(aim_last[1],aim_last[0]),-np.pi/2,np.pi/2)) / 90
#         use_target_to_aim = False
        
#         if use_target_to_aim:
#             angle_final = angle_target
#         else:
#             angle_final = angle_last
            
#         # angle_final = waypoints[-1][2] + np.pi
            
#         steer = self.turn_controller.step(angle_final)
#         steer = np.clip(steer,-1,1)
        
#         brake = abs(desired_speed) < self.brake_speed or (abs(speed) / abs(desired_speed)) > self.brake_ratio
                
#         delta = np.clip(speed - desired_speed,0,self.clip_delta)
#         throttle = self.speed_controller.step(delta)
#         throttle = np.clip(throttle,0.0,self.max_throttle)
#         throttle = throttle if not brake else 0.0
                
#         metadata = {
#             'speed': float(speed.astype(np.float64)),
#             'steer': float(steer),
#             'throttle': float(throttle),
#             'brake': float(brake),
#             # 'wp_4': tuple(waypoints[3].astype(np.float64)),
#             # 'wp_3': tuple(waypoints[2].astype(np.float64)),
#             # 'wp_2': tuple(waypoints[1].astype(np.float64)),
#             # 'wp_1': tuple(waypoints[0].astype(np.float64)),
#             'aim': tuple(aim.astype(np.float64)),
#             # 'target': tuple(target.astype(np.float64)),
#             'desired_speed': float(desired_speed.astype(np.float64)),
#             'angle': float(angle.astype(np.float64)),
#             # 'angle_last': float(angle_last.astype(np.float64)),
#             # 'angle_target': float(angle_target.astype(np.float64)),
#             'angle_final': float(angle_final.astype(np.float64)),
#             'delta': float(delta.astype(np.float64)),
#             'turn_KP': self.turn_KP,
#             'turn_KI': self.turn_KI,
#             'turn_KD': self.turn_KD,
#             'turn_n': self.turn_n,
#         }
                
#         # waypoints[:,1] = - waypoints[:,1]
#         gear = -1
#         manual_gear_shift = True
#         return gear, manual_gear_shift, steer, throttle, brake, metadata
#endregion

# 2.5 1.0 1.5 10
class ParkingController(object):
    def __init__(self, turn_KP=3.0, turn_KI=1.4, turn_KD=2.0, turn_n=15, speed_KP=5.0, speed_KI=0.5, speed_KD=1.0, speed_n=40, max_throttle=0.75, brake_speed=0.4, brake_ratio=1.1, clip_delta=0.25, aim_dist=1.0, angle_thresh=0.3, dist_thresh=10):
        self.turn_controller = PID(K_P=turn_KP, K_I=turn_KI, K_D=turn_KD, n=turn_n)
        self.speed_controller = HeadingPID(K_P=speed_KP, K_I=speed_KI, K_D=speed_KD, n=speed_n)
        self.max_throttle = max_throttle
        self.brake_speed = brake_speed
        self.brake_ratio = brake_ratio
        self.clip_delta = clip_delta
        self.aim_dist = aim_dist
        self.angle_thresh = angle_thresh
        self.dist_thresh = dist_thresh
        self.desired_speed = 0
        self.turn_KP = turn_KP
        self.turn_KI = turn_KI
        self.turn_KD = turn_KD
        self.turn_n = turn_n

    def reset(self):
        self.turn_controller.reset()
        self.speed_controller.reset()

    def control_pid(self, waypoints, speed, targets=None):
        print(f"WAYPOINTS SHAPE:{waypoints.shape}")
        # waypoints[:,1] = - waypoints[:,1]
        num_pairs = len(waypoints) - 1
        best_norm = 1e5
        if math.sqrt(waypoints[-1][0] ** 2 + waypoints[-1][1]**2) < 0.1:
            desired_speed = np.array([-0.2])
        else:
            desired_speed = np.array([-0.8])
        use_heading_control = True
        if use_heading_control:
            angle_last = -waypoints[-1][2]
            angle_last = np.degrees(angle_last) / 90
        # angle_last = np.degrees(np.pi / 2 - np.clip(np.arctan2(aim_last[1],aim_last[0]),-np.pi/2,np.pi/2)) / 90
        use_target_to_aim = False
        
        if use_target_to_aim:
            angle_final = angle_target
        else:
            angle_final = angle_last
            
        # angle_final = waypoints[-1][2] + np.pi
            
        steer = self.turn_controller.step(angle_final)
        steer = np.clip(steer,-1,1)
        
        if abs(speed) - abs(desired_speed) > 0.5:
            brake = abs(desired_speed) < self.brake_speed or (abs(speed) / abs(desired_speed)) > self.brake_ratio
        else:
            brake = 0
                
        delta = np.clip(speed - desired_speed,0,self.clip_delta)
        throttle = self.speed_controller.step(delta)
        throttle = np.clip(throttle,0.35,self.max_throttle)
        throttle = throttle if not brake else 0.0
                
        metadata = {
            'speed': float(speed.astype(np.float64)),
            'steer': float(steer),
            'throttle': float(throttle),
            'brake': float(brake),
            # 'wp_4': tuple(waypoints[3].astype(np.float64)),
            # 'wp_3': tuple(waypoints[2].astype(np.float64)),
            # 'wp_2': tuple(waypoints[1].astype(np.float64)),
            # 'wp_1': tuple(waypoints[0].astype(np.float64)),
            # 'aim': tuple(aim.astype(np.float64)),
            # 'target': tuple(target.astype(np.float64)),
            'desired_speed': float(desired_speed.astype(np.float64)),
            # 'angle': float(angle.astype(np.float64)),
            # 'angle_last': float(angle_last.astype(np.float64)),
            # 'angle_target': float(angle_target.astype(np.float64)),
            'angle_final': float(angle_final.astype(np.float64)),
            'delta': float(delta.astype(np.float64)),
            'turn_KP': self.turn_KP,
            'turn_KI': self.turn_KI,
            'turn_KD': self.turn_KD,
            'turn_n': self.turn_n,
            'waypoints':waypoints.tolist(),
        }
                
        # waypoints[:,1] = - waypoints[:,1]
        gear = -1
        manual_gear_shift = True
        return gear, manual_gear_shift, steer, throttle, brake, metadata
    
    