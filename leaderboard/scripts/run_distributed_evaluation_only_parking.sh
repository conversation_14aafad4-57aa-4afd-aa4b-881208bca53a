#!/bin/bash
# Must set CARLA_ROOT
export CARLA_ROOT='/public/home/<USER>/Bench2Drive_parking/Bench2DriveZoo/carla'
export CARLA_SERVER=${CARLA_ROOT}/CarlaUE4.sh
export PYTHONPATH=$PYTHONPATH:${CARLA_ROOT}/PythonAPI
export PYTHONPATH=$PYTHONPATH:${CARLA_ROOT}/PythonAPI/carla
export PYTHONPATH=$PYTHONPATH:/storage/group/4dvlab/wangjiamin/bevfusion_full/Bench2DriveZoo/
# export PYTHONPATH=$PYTHONPATH:$CARLA_ROOT/PythonAPI/carla/dist/carla-0.9.15-py3.7-linux-x86_64.egg
export PYTHONPATH=$PYTHONPATH:leaderboard
export PYTHONPATH=$PYTHONPATH:leaderboard/team_code
export PYTHONPATH=$PYTHONPATH:scenario_runner
export SCENARIO_RUNNER_ROOT=scenario_runner

export LEADERBOARD_ROOT=leaderboard
export CHALLENGE_TRACK_CODENAME=SENSORS
export PORT=$1
export TM_PORT=$2
export DEBUG_CHALLENGE=0
export REPETITIONS=1 # multiple evaluation runs
export RESUME=True
export IS_BENCH2DRIVE=$3
export PLANNER_TYPE=$8

# TCP evaluation
export TEAM_AGENT=$4
export TEAM_CONFIG=$5
export CHECKPOINT_ENDPOINT=$6
export SAVE_PATH=$7

python -m torch.distributed.launch --nproc_per_node=4 --nnodes=1 --node_rank=0 --master_port 5005 ${LEADERBOARD_ROOT}/leaderboard/only_parking_evaluator_full.py \
--checkpoint=${CHECKPOINT_ENDPOINT} \
--track=${CHALLENGE_TRACK_CODENAME} \
--agent=${TEAM_AGENT} \
--agent-config=${TEAM_CONFIG} \
--debug=${DEBUG_CHALLENGE} \
--resume=${RESUME} \
--port=${PORT} \
--traffic-manager-port=${TM_PORT} \
--timeout=1000000 \
--map=Town05
