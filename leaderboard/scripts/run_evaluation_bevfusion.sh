#!/bin/bash

export CARLA_ROOT='/storage/group/4dvlab/wangjiamin/Bench2Drive_parking/Bench2DriveZoo/carla'
export CARLA_SERVER=${CARLA_ROOT}/CarlaUE4.sh
# export PYTHONPATH=$PYTHONPATH:${CARLA_ROOT}/PythonAPI
# export PYTHONPATH=$PYTHONPATH:${CARLA_ROOT}/PythonAPI/carla
# export PYTHONPATH=$PYTHONPATH:$CARLA_ROOT/PythonAPI/carla/dist/carla-0.9.15-py3.7-linux-x86_64.egg
# export PYTHONPATH=$PYTHONPATH:leaderboard
# export PYTHONPATH=$PYTHONPATH:leaderboard/team_code
# export PYTHONPATH=$PYTHONPATH:scenario_runner
export SCENARIO_RUNNER_ROOT=scenario_runner
echo $PYTHONPATH
BASE_PORT=30000
BASE_TM_PORT=50000
IS_BENCH2DRIVE=True
# TEAM_AGENT=leaderboard/team_code/your_team_agent.py
TEAM_AGENT=/storage/group/4dvlab/wangjiamin/bevfusion_full/Bench2DriveZoo/team_code/diffusiondrive_bevfusion.py
# TEAM_CONFIG=your_team_agent_ckpt.pth   # for TCP and ADMLP
TEAM_CONFIG=/storage/group/4dvlab/wangjiamin/27334a61ff9d4fe886e22b85fd593502/data/exp_bevfusion_final/diffusiondrive_bevfusion_union.py+/storage/group/4dvlab/wangjiamin/27334a61ff9d4fe886e22b85fd593502/data/exp_bevfusion_final/epoch_50.pth
# TEAM_CONFIG=your_team_agent_config.py+your_team_agent_ckpt.pth # for UniAD and VAD
BASE_CHECKPOINT_ENDPOINT=eval
SAVE_PATH=./only_parking_test/
PLANNER_TYPE=only_traj

GPU_RANK=0
PORT=$BASE_PORT
TM_PORT=$BASE_TM_PORT
CHECKPOINT_ENDPOINT="${BASE_CHECKPOINT_ENDPOINT}.json"
bash leaderboard/scripts/run_distributed_evaluation_only_parking.sh $PORT $TM_PORT $IS_BENCH2DRIVE $TEAM_AGENT $TEAM_CONFIG $CHECKPOINT_ENDPOINT $SAVE_PATH $PLANNER_TYPE $GPU_RANK
