
import carla
import numpy as np
import pandas as pd
import glob

from datetime import datetime
import torch.distributed
from parking.world import World
from parking.positions import EgoPosTown05
import pathlib
import traceback
import argparse
from argparse import RawTextHelpFormatter
from distutils.version import LooseVersion
import importlib
import os
import pkg_resources
import sys
import carla
import signal
import torch

from srunner.scenariomanager.carla_data_provider import *
from srunner.scenariomanager.timer import GameTime
from srunner.scenariomanager.watchdog import Watchdog

from leaderboard.scenarios.only_parking_scenario_full import ParkingScenarioManagerEval
from leaderboard.scenarios.route_scenario import RouteScenario
from leaderboard.envs.sensor_interface import SensorConfigurationInvalid
from leaderboard.autoagents.agent_wrapper import AgentError, validate_sensor_configuration, TickRuntimeError
from leaderboard.utils.statistics_manager import StatisticsManager, FAILURE_MESSAGES
from leaderboard.utils.route_indexer import RouteIndexer
import atexit
import subprocess
import time
import random
from datetime import datetime
import socket
import torch.distributed as dist
import math

def find_free_port(starting_port):
    port = starting_port
    while True:
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(("localhost", port))
                return port
        except OSError:
            port += 1

sensors_to_icons = {
    'sensor.camera.rgb':        'carla_camera',
    'sensor.lidar.ray_cast':    'carla_lidar',
    'sensor.other.radar':       'carla_radar',
    'sensor.other.gnss':        'carla_gnss',
    'sensor.other.imu':         'carla_imu',
    'sensor.opendrive_map':     'carla_opendrive_map',
    'sensor.speedometer':       'carla_speedometer'
}
class ParkingEvaluatorWrapper:
    def __init__(self,args,statistics_manager,world_size,rank):
        self.args = args
        self.sensors = None
        self.seed = args.seed
        self.server = None
        self.world_size = world_size
        self.rank = rank
        self.args.port = self.args.port + rank * 10000

        # 让 rank 0 先获取时间戳，然后广播给所有进程，确保所有进程使用相同的文件夹名
        if rank == 0:
            now = datetime.now()
            timestamp = [now.year, now.month, now.day, now.hour, now.minute, now.second]
        else:
            timestamp = [0, 0, 0, 0, 0, 0]

        # 广播时间戳到所有进程（NCCL 需要 GPU tensor）
        print(f"[Rank {rank}] Synchronizing timestamp...", flush=True)
        timestamp_tensor = torch.tensor(timestamp, dtype=torch.int32, device=f'cuda:{rank}')
        try:
            dist.broadcast(timestamp_tensor, src=0)
            timestamp = timestamp_tensor.cpu().tolist()
            print(f"[Rank {rank}] Timestamp synchronized: {timestamp}", flush=True)
        except Exception as e:
            print(f"[Rank {rank}] ERROR: Failed to synchronize timestamp: {e}", flush=True)
            raise

        result_dir = '_'.join(map(lambda x: '%02d' % x, timestamp))
        self._eva_result_path = pathlib.Path(args.eva_result_path) / result_dir

        if rank == 0:
            self._eva_result_path.mkdir(parents=True, exist_ok=True)
            print(f"[Rank {rank}] Created result directory: {self._eva_result_path}", flush=True)

        print(f"[Rank {rank}] Waiting at barrier for all ranks to be ready...", flush=True)
        try:
            dist.barrier()
            print(f"[Rank {rank}] All ranks ready, proceeding...", flush=True)
        except Exception as e:
            print(f"[Rank {rank}] ERROR: Barrier failed: {e}", flush=True)
            raise

        self._eva_epochs = args.eva_epochs
        self._eva_task_nums = args.eva_task_nums
        self._eva_parking_nums = args.eva_parking_nums

    def safe_all_gather_with_timeout(self, success_value, timeout_seconds=30):
        """
        安全的 all_gather 操作，带超时机制
        """
        try:
            success_tensor = torch.tensor([1 if success_value else 0], dtype=torch.int32, device=f'cuda:{self.rank}')
            gathered_success = [torch.zeros(1, dtype=torch.int32, device=f'cuda:{self.rank}') for _ in range(dist.get_world_size())]

            # 使用超时的 all_gather
            start_time = time.time()
            dist.all_gather(gathered_success, success_tensor)
            elapsed = time.time() - start_time

            if elapsed > timeout_seconds:
                print(f"[Rank {self.rank}] WARNING: all_gather took {elapsed:.2f}s (timeout: {timeout_seconds}s)", flush=True)

            all_success = all(tensor.item() == 1 for tensor in gathered_success)
            success_status = [t.item() for t in gathered_success]

            return all_success, success_status

        except Exception as e:
            print(f"[Rank {self.rank}] ERROR in safe_all_gather: {e}", flush=True)
            # 如果同步失败，假设其他进程也失败了，返回 False
            return False, [0] * dist.get_world_size()

    def _initialize_evaluator(self):
        """初始化评估器的其他属性"""
        self._eva_epoch_idx = 0
        self._eva_task_idx = 0
        self._eva_parking_idx = 0
        
        self._x_diff_to_goal = sys.float_info.max
        self._y_diff_to_goal = sys.float_info.max
        self._distance_diff_to_goal = sys.float_info.max
        self._orientation_diff_to_goal = sys.float_info.max
        
        self._goal_reach_x_diff = 1.0              # meter
        self._goal_reach_y_diff = 0.6              # meter
        self._goal_reach_orientation_diff = 10.0   # degree
        
        self._frames_per_second = 10
        # metric frames
        self._num_frames_in_goal = 0
        self._num_frames_nearby_goal = 0
        self._num_frames_nearby_no_goal = 0
        self._num_frames_outbound = 0
        self._num_frames_total = 0
        self._num_frames_in_goal_needed = 10 # 2 * self._frames_per_second         # 2s
        self._num_frames_nearby_goal_needed = 2 * self._frames_per_second     # 2s
        self._num_frames_nearby_no_goal_needed = 2 * self._frames_per_second  # 2s
        self._num_frames_outbound_needed = 10 * self._frames_per_second       # 10s
        self._num_frames_total_needed = 50 * self._frames_per_second          # 30s

        # metric for 1 slot
        self._target_success_nums = 0
        self._target_fail_nums = 0
        self._no_target_success_nums = 0
        self._no_target_fail_nums = 0
        self._collision_nums = 0
        self._outbound_nums = 0
        self._timeout_nums = 0
        self._position_error = []
        self._orientation_error = []
        self._parking_time = []
        self._inference_time = []

        # metric rate for 16 slot
        self._target_success_rate = []
        self._target_fail_rate = []
        self._no_target_success_rate = []
        self._no_target_fail_rate = []
        self._collision_rate = []
        self._outbound_rate = []
        self._timeout_rate = []
        self._average_position_error = []
        self._average_orientation_error = []
        self._average_parking_time = []
        self._average_inference_time = []

        self._epoch_metric_info = {}

        # # In paper: NTR = NTSR + NTFR, TR = TR + OR
        self._metric_names = {
            "target_success_rate": "TSR",
            "target_fail_rate": "TFR",
            "no_target_success_rate": "NTSR",
            "no_target_fail_rate": "NTFR",
            "collision_rate": "CR",
            "outbound_rate": "OR",
            "timeout_rate": "TR",
            "average_position_error": "APE",
            "average_orientation_error": "AOE",
            "average_parking_time": "APT",
            "average_inference_time": "AIT",
        }

        self.manager = ParkingScenarioManagerEval(None,self.args.filter,self.args)
        module_name = os.path.basename(self.args.agent).split('.')[0]
        sys.path.insert(0, os.path.dirname(self.args.agent))
        self.module_agent = importlib.import_module(module_name)

        # 调用初始化方法
        self._initialize_evaluator()
        
    def _set_all_seed(self,value):
        random.seed(value)
        np.random.seed(value)
        torch.manual_seed(value)
        if torch.cuda.is_available():
            torch.cuda.manual_seed_all(value)
            torch.backends.cudnn.deterministic = True
            torch.backends.cudnn.benchmark = False
        
    def _setup_simulation(self,args):
        """
        Prepares the simulation by getting the client, and setting up the world and traffic manager settings
        """
        self.carla_path = os.environ["CARLA_ROOT"]
        # args.port = find_free_port(args.port)
        cmd1 = f"{os.path.join(self.carla_path, 'CarlaUE4.sh')} -RenderOffScreen -nosound -carla-rpc-port={args.port} -graphicsadapter={self.rank}"
        self.server = subprocess.Popen(cmd1, shell=True, preexec_fn=os.setsid)
        print(f"[Rank {self.rank}] Starting CARLA: {cmd1}", flush=True)
        print(f"[Rank {self.rank}] CARLA process PID: {self.server.pid}, returncode: {self.server.returncode}", flush=True)

        # Initial wait for CARLA to start (reduced from 60 to 40 seconds)
        print(f"[Rank {self.rank}] Waiting 40 seconds for CARLA to start...", flush=True)
        time.sleep(40)

        attempts = 0
        num_max_restarts = 30  # Increased from 20 to 30
        client = None
        client_timeout = 60.0  # Default timeout

        while attempts < num_max_restarts:
            try:
                print(f"[Rank {self.rank}] Attempting to connect to CARLA (attempt {attempts + 1}/{num_max_restarts})...", flush=True)
                client = carla.Client(args.host, args.port)
                if args.timeout:
                    client_timeout = args.timeout
                client.set_timeout(client_timeout)

                # Test connection first
                print(f"[Rank {self.rank}] Testing connection to CARLA server...", flush=True)
                version = client.get_server_version()
                print(f"[Rank {self.rank}] Connected to CARLA server version: {version}", flush=True)

                # Load world
                print(f"[Rank {self.rank}] Loading world: {args.map}", flush=True)
                client.load_world(args.map)

                settings = carla.WorldSettings(
                    synchronous_mode = True,
                    fixed_delta_seconds = 1.0 / self._frames_per_second,
                    deterministic_ragdolls = True,
                    spectator_as_ego = False
                )
                world = client.get_world()
                print(f"[Rank {self.rank}] Applying world settings: {settings}", flush=True)
                world.apply_settings(settings)
                print(f"[Rank {self.rank}] ✓ CARLA setup successful after {attempts + 1} attempts", flush=True)
                break
            except Exception as e:
                print(f"[Rank {self.rank}] ✗ CARLA connection failed (attempt {attempts + 1}/{num_max_restarts})", flush=True)
                print(f"[Rank {self.rank}] Error: {e}", flush=True)
                attempts += 1
                if attempts < num_max_restarts:
                    wait_time = 10
                    print(f"[Rank {self.rank}] Retrying in {wait_time} seconds...", flush=True)
                    time.sleep(wait_time)
                else:
                    print(f"[Rank {self.rank}] ✗✗✗ FATAL: Failed to connect to CARLA after {num_max_restarts} attempts", flush=True)
                    raise RuntimeError(f"Rank {self.rank}: Failed to connect to CARLA server after {num_max_restarts} attempts")

        if client is None:
            raise RuntimeError(f"Rank {self.rank}: Failed to initialize CARLA client")

        return client, client_timeout

    def _load_and_wait_for_world(self,args):
        CarlaDataProvider.set_client(self.client)
        CarlaDataProvider.set_world(self.world)
        self.world.tick()

    def prepare_env(self,):
        # set up the simulator
        self.client,self.client_timeout = self._setup_simulation(self.args)
        dist = pkg_resources.get_distribution("carla")
        if dist.version != 'leaderboard':
            if LooseVersion(dist.version) < LooseVersion('0.9.10'):
                raise ImportError("CARLA version 0.9.10.1 or newer required. CARLA version found: {}".format(dist))
        self.world = self.client.get_world()
        self.manager._connet_to_simulator(self.world,self.client)
        
    def run_scenario(self):
        last_log_frame = 0
        log_interval = 50  # Log every 50 frames

        while True:
            self.manager.distance_diff_to_goal = self._distance_diff_to_goal
            self.manager.rotation_diff_to_goal = self._orientation_diff_to_goal
            self.manager.x_diff_to_goal = self._x_diff_to_goal
            self.manager.y_diff_to_goal = self._y_diff_to_goal

            self._num_frames_total += 1

            # Log progress periodically
            if self._num_frames_total - last_log_frame >= log_interval:
                print(f"[Rank {self.rank}] Task {self._eva_task_idx}, Attempt {self._eva_parking_idx}: "
                      f"Frame {self._num_frames_total}/{self._num_frames_total_needed}, "
                      f"Distance to goal: {self._distance_diff_to_goal:.2f}m", flush=True)
                last_log_frame = self._num_frames_total

            self.manager._setup_watchdog(self.agent_instance)
            # detect collision
            is_collision = self.manager._tick_scenario(self.agent_instance)
            if is_collision:
                self._collision_nums += 1
                print(f"[Rank {self.rank}] Parking collision for task {self.manager._parking_goal_index}-{self._eva_parking_idx + 1}, "
                      f"collision_num: {self._collision_nums}", flush=True)
                return

            if self._num_frames_total > self._num_frames_total_needed:
                self._timeout_nums += 1
                print(f"[Rank {self.rank}] Task {self._eva_task_idx}, Attempt {self._eva_parking_idx}: TIMEOUT after {self._num_frames_total} frames", flush=True)
                return

            is_success = self.eva_check_goal()
            if is_success:
                print(f"[Rank {self.rank}] Task {self._eva_task_idx}, Attempt {self._eva_parking_idx}: SUCCESS at frame {self._num_frames_total}", flush=True)
                return
        
    def eva_check_goal(self):
        player = self.manager.player
        # TODO: t.y = -t.y?
        t = player.get_transform().location
        # t.y = -t.y
        r = player.get_transform().rotation
        v = player.get_velocity()
        c = player.get_control()
        speed = (3.6 * math.sqrt(v.x ** 2 + v.y ** 2 + v.z ** 2))
        # find the closest goal
        closest_goal = [0.0, 0.0]
        self._x_diff_to_goal = sys.float_info.max
        self._y_diff_to_goal = sys.float_info.max
        self._distance_diff_to_goal = sys.float_info.max
        self._orientation_diff_to_goal = min(abs(r.yaw),180-abs(r.yaw))
        parking_goal = self.manager._parking_spawn_points[self.manager._parking_goal_index]
        for parking_spawn_point in self.manager._parking_spawn_points:
            if t.distance(parking_spawn_point) < self._distance_diff_to_goal:
                self._distance_diff_to_goal = t.distance(parking_spawn_point)
                self._x_diff_to_goal = abs(t.x - parking_spawn_point.x)
                self._y_diff_to_goal = abs(t.y - parking_spawn_point.y)
                closest_goal[0] = parking_spawn_point.x
                closest_goal[1] = parking_spawn_point.y
        if self.check_success_slot(closest_goal,t):
            return True
        
        
    def check_success_slot(self,closest_goal,ego_transform):
        x_in_slot = (abs(ego_transform.x - closest_goal[0]) <= self._goal_reach_x_diff)
        y_in_slot = (abs(ego_transform.y - closest_goal[1]) <= self._goal_reach_y_diff)
        r_in_slot = (self._orientation_diff_to_goal <= self._goal_reach_orientation_diff)
        if x_in_slot and y_in_slot and r_in_slot:
            self._num_frames_in_goal += 1
        if self._num_frames_in_goal > self._num_frames_in_goal_needed:
            parking_goal = self.manager._parking_spawn_points[self.manager._parking_goal_index]
            parking_goal = [parking_goal.x,parking_goal.y]
            if (parking_goal[0] == closest_goal[0]) and (parking_goal[1] == closest_goal[1]):
                self._target_success_nums += 1
                self._position_error.append(self._distance_diff_to_goal)
                self._orientation_error.append(self._orientation_diff_to_goal)
                self._parking_time.append(self._num_frames_total / self._frames_per_second)
                print(f"parking target success for task {self._eva_parking_idx + 1}, target_success_nums: {self._target_success_nums}")
            else:
                self._no_target_success_nums += 1
                print(f"parking no target success for task {self._eva_parking_idx + 1}, no_target_success_nums: {self._no_target_success_nums}")
            return True

        return False
            
    def start(self):
        while self._eva_epoch_idx < self._eva_epochs:
            print(f"[Rank {self.rank}] ==========================Epoch {self._eva_epoch_idx} ===============================", flush=True)
            epoch_string = f"epoch_{self._eva_epoch_idx:02d}"
            save_epoch_path_dir = self._eva_result_path / epoch_string
            if self.rank == 0:
                save_epoch_path_dir.mkdir(parents=True, exist_ok=True)
                print(f"[Rank {self.rank}] Created epoch directory: {save_epoch_path_dir}", flush=True)

            print(f"[Rank {self.rank}] Waiting at epoch barrier...", flush=True)
            try:
                dist.barrier()
                print(f"[Rank {self.rank}] All ranks synchronized for epoch {self._eva_epoch_idx}", flush=True)
            except Exception as e:
                print(f"[Rank {self.rank}] ERROR: Epoch barrier failed: {e}", flush=True)
                raise
            self.random_rng = random.Random(self.seed)
            self._eva_task_idx = 1
            free_positions = self.manager.get_all_free_parking_position()
            free_positions_indices = [0,3,8,10,15,17,21,22,24,28,31,35]
            task_num = len(free_positions_indices) // self.world_size
            self._eva_task_idx = task_num * self.rank
            while task_num:
                print(f"[Rank {self.rank}] ==========================Task {self._eva_task_idx} ===============================", flush=True)
                target_position_idx = free_positions[free_positions_indices[self._eva_task_idx]]
                self._set_all_seed(self.seed + self._eva_epoch_idx)
                self._eva_parking_idx = 0
                while self._eva_parking_idx < self._eva_parking_nums:
                    print(f"[Rank {self.rank}] ==========================Attempt {self._eva_parking_idx} ===============================", flush=True)

                    # 标记当前尝试是否成功，用于同步
                    attempt_success = False

                    try:
                        self.prepare_env()
                        print(f"[Rank {self.rank}] CARLA connected, waiting for all ranks before init_scenarios...", flush=True)

                        # 安全的 barrier：所有进程都必须到达这里
                        dist.barrier()
                        print(f"[Rank {self.rank}] All ranks connected to CARLA, proceeding with init_scenarios", flush=True)

                        # 尝试初始化场景
                        self.manager.init_scenarios(self.random_rng,self.args,target_position_idx)
                        attempt_success = True
                        print(f"[Rank {self.rank}] init_scenarios successful", flush=True)

                    except Exception as e:
                        print(f"[Rank {self.rank}] Error in prepare_env or init_scenarios: {e}", flush=True)
                        attempt_success = False
                        self.destroy()

                    # 同步所有进程的成功状态
                    all_success, success_status = self.safe_all_gather_with_timeout(attempt_success, timeout_seconds=60)
                    print(f"[Rank {self.rank}] Attempt success status: {success_status}, all_success: {all_success}", flush=True)

                    if not all_success:
                        print(f"[Rank {self.rank}] Some ranks failed, all ranks will retry", flush=True)
                        self._eva_parking_idx += 1
                        if attempt_success:  # 如果当前进程成功了但其他进程失败，也需要清理
                            self.destroy()
                        continue
                    task_string = f"task_{self._eva_task_idx:02d}"
                    save_task_path_dir = save_epoch_path_dir / task_string
                    save_task_path_dir.mkdir(parents=True,exist_ok=True)
                    GameTime.restart()
                    self._num_frames_total = 0
                    self._num_frames_in_goal = 0
                    town_name = str(self.args.map)
                    currentDateAndTime = datetime.now()
                    currentTime = currentDateAndTime.strftime("%m_%d_%H_%M_%S")
                    save_name = f"{self._eva_parking_idx}"
                    
                    agent_class_name = getattr(self.module_agent,'get_entry_point')()
                    agent_class_obj = getattr(self.module_agent,agent_class_name)
                    
                    if getattr(agent_class_obj,'get_ros_version')() == 1 and self._ros1_server is None:
                        from leaderboard.autoagents.ros1_agent import ROS1Server
                        self._ros1_server = ROS1Server()
                        self._ros1_server.start()
                    try:
                        self._load_and_wait_for_world(self.args)
                    except Exception:
                        # The scenario is wrong -> set the ejecution to crashed and stop
                        print("\n\033[91mThe scenario could not be loaded:", flush=True)
                        print(f"\n{traceback.format_exc()}\033[0m", flush=True)

                        entry_status, crash_message = FAILURE_MESSAGES["Simulation"]
                        self.destroy()
                        continue
                    # Agent setup 阶段
                    agent_setup_success = False
                    try:
                        agent_class_name = getattr(self.module_agent,'get_entry_point')()
                        agent_class_obj = getattr(self.module_agent,agent_class_name)

                        if getattr(agent_class_obj,'get_ros_version')() == 1 and self._ros1_server is None:
                            from leaderboard.autoagents.ros1_agent import ROS1Server
                            self._ros1_server = ROS1Server()
                            self._ros1_server.start()

                        self.agent_instance = agent_class_obj(self.args.host,self.args.port,self.args.debug)
                        agent_config = self.args.agent_config + '+' + save_name
                        print(f"[Rank {self.rank}] Setting up agent...", flush=True)
                        self.agent_instance.setup(agent_config)
                        print(f"[Rank {self.rank}] Agent setup complete", flush=True)

                        target_position = self.manager.get_target_position()
                        self.agent_instance._set_target_position(target_position)
                        self.agent_instance._set_vehicles(self.manager.get_ego_vehicle())
                        if not self.sensors:
                            self.sensors = self.agent_instance.sensors()
                            track = self.agent_instance.track
                            validate_sensor_configuration(self.sensors, track, self.args.track)

                            self.sensor_icons = [sensors_to_icons[sensor['type']] for sensor in self.sensors]

                            self.sensors_initialized = True

                        agent_setup_success = True
                        print(f"[Rank {self.rank}] Agent setup and sensor validation successful", flush=True)

                    except SensorConfigurationInvalid as e:
                        print(f"[Rank {self.rank}] Sensor configuration invalid: {e}", flush=True)
                        agent_setup_success = False
                        self.destroy()
                    except Exception as e:
                        print(f"[Rank {self.rank}] Error in agent setup: {e}", flush=True)
                        agent_setup_success = False
                        self.destroy()

                    # 同步所有进程的 agent setup 状态
                    all_setup_success, setup_status = self.safe_all_gather_with_timeout(agent_setup_success, timeout_seconds=120)
                    print(f"[Rank {self.rank}] Agent setup status: {setup_status}, all_success: {all_setup_success}", flush=True)

                    if not all_setup_success:
                        print(f"[Rank {self.rank}] Some ranks failed agent setup, all ranks will retry", flush=True)
                        self._eva_parking_idx += 1
                        if agent_setup_success:  # 如果当前进程成功了但其他进程失败，也需要清理
                            self.agent_instance.destroy()
                            self.destroy()
                        continue
                    # Scenario 运行阶段
                    scenario_success = False
                    try:
                        self.manager.load_scenario(self.agent_instance)
                        self.agent_instance.set_save_path(save_task_path_dir / save_name)
                        self.run_scenario()
                        scenario_success = True
                        print(f"[Rank {self.rank}] Scenario completed successfully", flush=True)
                    except Exception as e:
                        print(f"[Rank {self.rank}] Error in scenario: {e}", flush=True)
                        scenario_success = False
                        self.destroy()
                        if hasattr(self, 'agent_instance'):
                            self.agent_instance.destroy()

                    # 同步所有进程的 scenario 状态
                    all_scenario_success, scenario_status = self.safe_all_gather_with_timeout(scenario_success, timeout_seconds=300)
                    print(f"[Rank {self.rank}] Scenario status: {scenario_status}, all_success: {all_scenario_success}", flush=True)

                    if not all_scenario_success:
                        print(f"[Rank {self.rank}] Some ranks failed scenario, all ranks will retry", flush=True)
                        self._eva_parking_idx += 1
                        if scenario_success:  # 如果当前进程成功了但其他进程失败，也需要清理
                            self.agent_instance.destroy()
                            self.destroy()
                        continue
                    # self.manager.load_scenario(self.agent_instance)
                    # self.agent_instance.set_save_path(save_task_path_dir / save_name)
                    # self.run_scenario()
                    self._eva_parking_idx += 1
                    self.agent_instance.destroy()
                    self.destroy()

                print(f"[Rank {self.rank}] Completed task {self._eva_task_idx}, saving metrics...", flush=True)
                self.save_slot_metric()
                self._eva_task_idx += 1
                task_num -= 1
                self.clear_metric_num()
                self.clear_metric_frame()
                print(f"[Rank {self.rank}] Task completed, {task_num} tasks remaining", flush=True)
            self.save_epoch_metric_csv(save_epoch_path_dir)
            self.clear_metric_rate()
            self._eva_epoch_idx += 1

            # Synchronize all ranks before moving to next epoch
            print(f"[Rank {self.rank}] Finished epoch {self._eva_epoch_idx - 1}, waiting for all ranks...", flush=True)
            try:
                dist.barrier()
                print(f"[Rank {self.rank}] All ranks finished epoch {self._eva_epoch_idx - 1}, moving to next epoch", flush=True)
            except Exception as e:
                print(f"[Rank {self.rank}] ERROR: End-of-epoch barrier failed: {e}", flush=True)
                raise
                    
            
        
    def destroy(self):
        # self._clean_up()
        self.manager.destroy()
        if hasattr(self, 'world'):
            del self.world
        if hasattr(self, 'client'):
            del self.client

        if self.server is not None:
            print(f"Attempting to kill server PID: {self.server.pid} (Rank {self.rank})")
            try:
                os.killpg(self.server.pid, signal.SIGINT)
                self.server.wait(timeout=5)
                print(f"Rank {self.rank}: Server terminated gracefully.")
            except Exception:
                print(f"Rank {self.rank}: Graceful termination failed, forcing kill.")
                os.killpg(self.server.pid, signal.SIGKILL)
            print(f"Attempting to kill server PID: {self.server.pid}")
            self.server = None
            
    
    
    def save_slot_metric(self):
        TSR = (self._target_success_nums / float(self._eva_parking_nums)) * 100.0
        TFR = (self._target_fail_nums / float(self._eva_parking_nums)) * 100.0
        NTSR = (self._no_target_success_nums / float(self._eva_parking_nums)) * 100.0
        NTFR = (self._no_target_fail_nums / float(self._eva_parking_nums)) * 100.0
        CR = (self._collision_nums / float(self._eva_parking_nums)) * 100.0
        OR = (self._outbound_nums / float(self._eva_parking_nums)) * 100.0
        TR = (self._timeout_nums / float(self._eva_parking_nums)) * 100.0
        # 处理空列表的情况，避免 nan
        APE = np.mean(self._position_error) if len(self._position_error) > 0 else 0.0
        AOE = np.mean(self._orientation_error) if len(self._orientation_error) > 0 else 0.0
        APT = np.mean(self._parking_time) if len(self._parking_time) > 0 else 0.0
        # AIT = np.mean(self._inference_time)

        # slot_id = parking_position.slot_id[self._eva_task_idx]
        task_id = self._eva_epoch_idx * self._eva_task_nums + self._eva_task_idx
        self._epoch_metric_info[task_id] = {
            'target_success_rate': TSR,
            'target_fail_rate': TFR,
            'no_target_success_rate': NTSR,
            'no_target_fail_rate': NTFR,
            'collision_rate': CR,
            'outbound_rate': OR,
            'timeout_rate': TR,
            'average_position_error': APE,
            'average_orientation_error': AOE,
            'average_parking_time': APT,
            # 'average_inference_time': AIT,
        }

        self._target_success_rate.append(TSR)
        self._target_fail_rate.append(TFR)
        self._no_target_success_rate.append(NTSR)
        self._no_target_fail_rate.append(NTFR)
        self._collision_rate.append(CR)
        self._outbound_rate.append(OR)
        self._timeout_rate.append(TR)
        self._average_position_error.append(APE)
        self._average_orientation_error.append(AOE)
        self._average_parking_time.append(APT)
        # self._average_inference_time.append(AIT)
    
    # def save_mean_std_csv(self):
    #     df_mean = pd.DataFrame()
    #     df_std = pd.DataFrame()
    #     csv_files = glob.glob(f'{self._eva_result_path}/*_result.csv')
    #     for i in range(self._eva_task_nums):
    #         df_row_i = pd.DataFrame()
    #         for csv in csv_files:
    #             df_csv = pd.read_csv(csv)
    #             row_i = df_csv.iloc[[i]]
    #             df_row_i = df_row_i.append(row_i)

    #         row_i_mean = df_row_i.mean(axis=0).to_frame().T
    #         row_i_std = (df_row_i.std(axis=0, ddof=0).to_frame().T / math.sqrt(6))

    #         df_mean = pd.concat([df_mean, row_i_mean], axis=0)
    #         df_std = pd.concat([df_std, row_i_std], axis=0)

    #     row_mean = df_mean.mean(axis=0).to_frame().T
    #     row_std = df_std.mean(axis=0).to_frame().T
    #     df_mean = pd.concat([df_mean, row_mean], axis=0)
    #     df_std = pd.concat([df_std, row_std], axis=0)

    #     all_name = ['2-1', '2-3', '2-5', '2-7', '2-9', '2-11', '2-13', '2-15',
    #                 '3-1', '3-3', '3-5', '3-7', '3-9', '3-11', '3-13', '3-15']
    #     name = all_name[:self._eva_task_nums]
    #     name.append('Avg')

    #     df_mean.index = name
    #     df_std.index = name

    #     pd.set_option('display.max_columns', 1000)
    #     pd.options.display.float_format = '{:,.3f}'.format

    #     logging.info('Mean')
    #     print(df_mean)

    #     logging.info('Std')
    #     print(df_std)

    #     df_mean.to_csv(self._eva_result_path / 'result_mean.csv')
    #     df_std.to_csv(self._eva_result_path / 'result_std.csv')
        
        
    def save_epoch_metric_csv(self, save_epoch_path_dir):
        # 处理空列表的情况，避免 nan
        local_metrics = {
            'target_success_rate': np.mean(self._target_success_rate) if len(self._target_success_rate) > 0 else 0.0,
            'target_fail_rate': np.mean(self._target_fail_rate) if len(self._target_fail_rate) > 0 else 0.0,
            'no_target_success_rate': np.mean(self._no_target_success_rate) if len(self._no_target_success_rate) > 0 else 0.0,
            'no_target_fail_rate': np.mean(self._no_target_fail_rate) if len(self._no_target_fail_rate) > 0 else 0.0,
            'collision_rate': np.mean(self._collision_rate) if len(self._collision_rate) > 0 else 0.0,
            'outbound_rate': np.mean(self._outbound_rate) if len(self._outbound_rate) > 0 else 0.0,
            'timeout_rate': np.mean(self._timeout_rate) if len(self._timeout_rate) > 0 else 0.0,
            'average_position_error': np.mean(self._average_position_error) if len(self._average_position_error) > 0 else 0.0,
            'average_orientation_error': np.mean(self._average_orientation_error) if len(self._average_orientation_error) > 0 else 0.0,
            'average_parking_time': np.mean(self._average_parking_time) if len(self._average_parking_time) > 0 else 0.0,
        }

        if torch.distributed.is_initialized():
            rank = torch.distributed.get_rank()
            world_size = torch.distributed.get_world_size()

            # 收集所有进程的 epoch_metric_info（所有进程都参与）
            gathered_epoch_info = [None for _ in range(world_size)]
            torch.distributed.all_gather_object(gathered_epoch_info, self._epoch_metric_info)

            # 收集所有进程的 local_metrics（所有进程都参与）
            local_tensors = [torch.tensor([v], dtype=torch.float32, device=f'cuda:{self.rank}')
                           for v in local_metrics.values()]
            gathered_metrics = [
                [torch.zeros(1, dtype=torch.float32, device=f'cuda:{self.rank}')
                for _ in range(world_size)]
                for _ in local_metrics
            ]
            for i, local_tensor in enumerate(local_tensors):
                torch.distributed.all_gather(gathered_metrics[i], local_tensor)

            # 只有 rank 0 进行数据处理和保存
            if rank == 0:
                # 合并所有进程的任务数据
                combined_epoch_info = {}
                for proc_info in gathered_epoch_info:
                    combined_epoch_info.update(proc_info)

                # 计算全局平均值
                keys = list(local_metrics.keys())
                final_avg_metrics = {}
                for i, key in enumerate(keys):
                    all_results = [t.item() for t in gathered_metrics[i]]
                    final_avg_metrics[key] = np.mean(all_results)

                combined_epoch_info['Avg'] = final_avg_metrics
                info_df = pd.DataFrame(combined_epoch_info)
                csv_name = 'eva_epoch_' + str(self._eva_epoch_idx + 1) + '_result.csv'
                self.save_csv(info_df, csv_name, save_epoch_path_dir)
                print(f"eva epoch {self._eva_epoch_idx + 1}")
        else:
            # 单进程情况
            self._epoch_metric_info['Avg'] = local_metrics
            info_df = pd.DataFrame(self._epoch_metric_info)
            csv_name = 'eva_epoch_' + str(self._eva_epoch_idx + 1) + '_result.csv'
            self.save_csv(info_df, csv_name, save_epoch_path_dir)
            print(f"eva epoch {self._eva_epoch_idx + 1}")

    def save_csv(self, info_df, csv_name,save_epoch_path_dir):
        info_df = info_df.T
        info_df.rename(columns=self._metric_names, inplace=True)
        pd.set_option('display.max_columns', 1000)
        pd.options.display.float_format = '{:,.3f}'.format
        print(info_df)
        info_df.to_csv(save_epoch_path_dir / csv_name)
        
    def clear_metric_num(self):
        self._target_success_nums = 0
        self._target_fail_nums = 0
        self._no_target_success_nums = 0
        self._no_target_fail_nums = 0
        self._collision_nums = 0
        self._outbound_nums = 0
        self._timeout_nums = 0
        self._position_error = []
        self._orientation_error = []
        self._inference_time = []
        self._parking_time = []

    def clear_metric_frame(self):
        self._num_frames_in_goal = 0
        self._num_frames_nearby_goal = 0
        self._num_frames_outbound = 0
        self._num_frames_total = 0

    def clear_metric_rate(self):
        self._target_success_rate = []
        self._target_fail_rate = []
        self._no_target_success_rate = []
        self._no_target_fail_rate = []
        self._collision_rate = []
        self._outbound_rate = []
        self._timeout_rate = []
        self._average_position_error = []
        self._average_orientation_error = []
        self._average_inference_time = []
        self._average_parking_time = []
        
    def _clean_up(self,):
        """
        Remove and destroy all actors
        """
        CarlaDataProvider.cleanup()

        if self.manager:
            self._client_timed_out = not self.manager.get_running_status()
            self.manager.cleanup()

        # Make sure no sensors are left streaming
        alive_sensors = self.world.get_actors().filter('*sensor*')
        for sensor in alive_sensors:
            sensor.stop()
            sensor.destroy()
        CarlaDataProvider.get_world().tick()
        time.sleep(5)
        

def main():
    description = "CARLA AD Leaderboard Evaluation: evaluate your Agent in Parking CARLA scenarios\n"

    # general parameters
    parser = argparse.ArgumentParser(description=description, formatter_class=RawTextHelpFormatter)
    parser.add_argument('--host', default='localhost',
                        help='IP of the host server (default: localhost)')
    parser.add_argument('--port', default=2000, type=int,
                        help='TCP port to listen to (default: 2000)')
    parser.add_argument('--traffic-manager-port', default=8000, type=int,
                        help='Port to use for the TrafficManager (default: 8000)')
    parser.add_argument('--traffic-manager-seed', default=0, type=int,
                        help='Seed used by the TrafficManager (default: 0)')
    parser.add_argument('--debug', type=int,
                        help='Run with debug output', default=0)
    parser.add_argument('--timeout', default=30.0, type=float,
                        help='Set the CARLA client timeout value in seconds')
    parser.add_argument("--track", type=str, default='SENSORS',
                        help="Participation track: SENSORS, MAP")
    parser.add_argument(
        '--seed',
        default=42,
        type=int,
        help='random seed for simulation')
    parser.add_argument(
        '--filter',
        metavar='PATTERN',
        default='vehicle.bmw.*',
        help='actor filter (default: "vehicle.*")')
    parser.add_argument(
        '--map',
        metavar="MAP",
        default='Town05',
        help='Map type')
    parser.add_argument(
        '--number-of-vehicles',
        metavar="Num",
        default=10,
        help='number of vehicles (default: 10)')
    # agent-related options
    parser.add_argument("-a", "--agent", type=str,
                        help="Path to Agent's py file to evaluate", required=True)
    parser.add_argument("--agent-config", type=str,
                        help="Path to Agent's configuration file", default="")

    parser.add_argument('--resume', type=bool, default=False,
                        help='Resume execution from last checkpoint?')
    parser.add_argument("--checkpoint", type=str, default='./simulation_results.json',
                        help="Path to checkpoint used for saving statistics and resuming")

    parser.add_argument("--gpu-rank", type=int, default=0)
    parser.add_argument("--eva-result-path",type=str,default='test',help='save result path')
    parser.add_argument('--eva-epochs',type=int,default=8,help='eval epochs')
    parser.add_argument('--eva-task-nums',type=int,default=32,help='eval tasks')
    parser.add_argument('--eva-parking-nums',type=int,default=1,help='attemp times')
    parser.add_argument('--local-rank',
                        default=0,
                        type=int,
                        help="local_rank")
    
    arguments = parser.parse_args()
    world_size = int(os.getenv('WORLD_SIZE',1))
    rank = int(os.getenv("RANK",0))
    dist.init_process_group('nccl',world_size=world_size,rank=rank)
    torch.cuda.set_device(f'cuda:{rank}')
    
    parking_evaluator = ParkingEvaluatorWrapper(arguments,None,world_size,rank)

    parking_evaluator.start()
    

if __name__ == "__main__":
    main()
