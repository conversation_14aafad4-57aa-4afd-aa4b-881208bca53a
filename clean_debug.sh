#!/bin/bash

# --- 杀死 evaluation 相关进程 ---
echo "正在查找并杀死所有 'evaluation' 相关的进程..."
# 使用 grep -v grep 过滤掉 grep 自身的进程
# 使用 awk '{print $2}' 提取进程ID (PID)
# 使用 xargs kill -9 强制终止这些进程
ps -aux | grep 'evaluation' | grep -v 'grep' | awk '{print $2}' | xargs kill -9

if [ $? -eq 0 ]; then
  echo "成功杀死 'evaluation' 进程。"
else
  echo "没有找到 'evaluation' 进程或杀死失败。"
fi

echo "-------------------------------------"

# --- 杀死 Carla 相关进程 ---
echo "正在查找并杀死所有 'Carla' 相关的进程..."
ps -aux | grep 'Carla' | grep -v 'grep' | awk '{print $2}' | xargs kill -9

if [ $? -eq 0 ]; then
  echo "成功杀死 'Carla' 进程。"
else
  echo "没有找到 'Carla' 进程或杀死失败。"
fi

echo "-------------------------------------"
echo "任务完成。"